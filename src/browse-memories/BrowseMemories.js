import React, { useState, useEffect, useRef } from 'react';
import { Layout, Typography, Button, Input, List, Tag, Card, Empty, Select, Space, Divider, message, Modal, Image, Spin, Popconfirm } from 'antd';
import { ArrowLeftOutlined, SearchOutlined, TagOutlined, ClockCircleOutlined, DeleteOutlined, EditOutlined, BulbOutlined, HeartOutlined, StarOutlined, BookOutlined, FlagOutlined, TrophyOutlined, SmileOutlined, HomeOutlined, ShopOutlined, VideoCameraOutlined, PlayCircleOutlined, SoundOutlined, PauseOutlined, LoadingOutlined } from '@ant-design/icons';
import { storageService, ossStorageService, searchIndexService, textToSpeechService } from '../services';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph } = Typography;

const { Search } = Input;

// 根据图标名称渲染图标
const renderIcon = (iconName) => {
  switch (iconName) {
    case 'BulbOutlined':
      return <BulbOutlined />;
    case 'HeartOutlined':
      return <HeartOutlined />;
    case 'StarOutlined':
      return <StarOutlined />;
    case 'BookOutlined':
      return <BookOutlined />;
    case 'FlagOutlined':
      return <FlagOutlined />;
    case 'TagOutlined':
      return <TagOutlined />;
    case 'TrophyOutlined':
      return <TrophyOutlined />;
    case 'SmileOutlined':
      return <SmileOutlined />;
    case 'HomeOutlined':
      return <HomeOutlined />;
    case 'ShopOutlined':
      return <ShopOutlined />;
    default:
      return <TagOutlined />;
  }
};

// 获取图片URL的辅助函数
const getMemoryImageUrl = async (memory, image, size = 'thumbnail') => {
  if (!memory || !image || !image.id) return '';

  try {
    return await ossStorageService.getImageUrl(memory.id, image.id, size);
  } catch (error) {
    console.error('获取图片URL失败:', error);
    return '';
  }
};

// 获取视频缩略图URL的辅助函数
const getMemoryVideoThumbnailUrl = async (memory, video) => {
  if (!memory || !video || !video.id) return '';

  try {
    // 使用特定的类型'thumbnail'来获取视频缩略图
    console.log(`获取视频缩略图: memory=${memory.id}, video=${video.id}`);
    const url = await ossStorageService.getVideoUrl(memory.id, video.id, 'thumbnail');
    console.log(`获取到视频缩略图URL: ${url}`);
    return url;
  } catch (error) {
    console.error('获取视频缩略图URL失败:', error);
    return '';
  }
};

const BrowseMemories = () => {
  const [memories, setMemories] = useState([]);
  const [displayedMemories, setDisplayedMemories] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTag, setSelectedTag] = useState('all');
  const [allTags, setAllTags] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const pageSize = 10; // 每页显示10条记忆

  // 图片预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  // 视频播放相关状态
  const [videoVisible, setVideoVisible] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');
  const [videoTitle, setVideoTitle] = useState('');
  const videoRef = useRef(null);

  // 文本转语音相关状态
  const [speakingMemoryId, setSpeakingMemoryId] = useState(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isLoadingSpeech, setIsLoadingSpeech] = useState(false);
  const [ttsInitialized, setTtsInitialized] = useState(false);

  // 初始化文本转语音服务
  useEffect(() => {
    const initializeTTS = async () => {
      try {
        const settings = await storageService.getSettings();
        const ttsSettings = settings?.ttsSettings || {};
        const initialized = await textToSpeechService.initialize(ttsSettings);
        setTtsInitialized(initialized);
        console.log('文本转语音服务初始化结果:', initialized);
      } catch (error) {
        console.error('初始化文本转语音服务失败:', error);
        setTtsInitialized(false);
      }
    };

    initializeTTS();
  }, []);

  useEffect(() => {
    // 加载分类
    const loadCategories = async () => {
      try {
        // 先从设置中加载分类，确保分类始终可用
        const settings = await storageService.getSettings();
        if (settings && settings.categories && settings.categories.length > 0) {
          console.log('从设置中加载分类:', settings.categories);
          setCategories(settings.categories);
        }
      } catch (error) {
        console.error('加载分类失败:', error);
      }
    };

    // 使用OSS存储服务加载记忆和分类
    const loadData = async () => {
      try {
        setLoading(true);
        message.info('正在加载记忆...');

        // 先加载分类，确保分类标签可用
        await loadCategories();

        // 初始化OSS存储服务
        console.log('正在初始化OSS存储服务...');
        const initialized = await ossStorageService.initialize();
        console.log('OSS存储服务初始化结果:', initialized);

        let loadedMemories = [];

        if (initialized) {
          // 使用OSS存储服务加载记忆
          try {
            // 获取记忆列表，并加载缩略图
            console.log('正在获取记忆列表...');
            const result = await ossStorageService.getMemoryList({}, true); // 第二个参数设置为true，表示加载缩略图
            console.log('记忆列表结果:', result);

            // 检查返回结果的结构
            if (!result || typeof result !== 'object') {
              console.error('返回的记忆列表结果无效:', result);
              throw new Error('返回的记忆列表结果无效');
            }

            const memoryList = result.memories || [];
            console.log('记忆列表长度:', memoryList.length);

            // 检查记忆列表是否为数组
            if (!Array.isArray(memoryList)) {
              console.error('记忆列表不是数组:', memoryList);
              throw new Error('记忆列表不是数组');
            }

            if (memoryList && memoryList.length > 0) {
              // 处理每个记忆的图片和视频URL
              const processedMemories = await Promise.all(memoryList.map(async (memory) => {
                // 注意：现在我们已经在OssStorageService中加载了最多9张图片和视频的缩略图
                // 这里只需要处理缺失的情况

                // 如果记忆没有缩略图，尝试从第一张图片获取
                if (!memory.thumbnail_url && memory.has_images) {
                  try {
                    if (memory.images && memory.images.length > 0) {
                      const firstImage = memory.images[0];
                      if (firstImage && firstImage.id && firstImage.thumbnail_url) {
                        memory.thumbnail_url = firstImage.thumbnail_url;
                        console.log('从第一张图片设置记忆缩略图');
                      } else if (firstImage && firstImage.id) {
                        // 如果图片没有缩略图，尝试获取
                        memory.thumbnail_url = await getMemoryImageUrl(memory, firstImage);
                        console.log('获取并设置记忆缩略图');
                      }
                    }
                  } catch (error) {
                    console.error('获取记忆缩略图失败:', error);
                  }
                }

                // 确保所有图片都有缩略图
                if (memory.images && memory.images.length > 0) {
                  for (const image of memory.images) {
                    if (!image.thumbnail_url && image.id) {
                      try {
                        image.thumbnail_url = await getMemoryImageUrl(memory, image);
                      } catch (error) {
                        console.error('获取图片缩略图失败:', error);
                      }
                    }
                  }
                }

                // 如果没有图片缩略图，尝试从第一个视频获取
                if (!memory.thumbnail_url && memory.has_videos) {
                  try {
                    if (memory.videos && memory.videos.length > 0) {
                      const firstVideo = memory.videos[0];
                      if (firstVideo && firstVideo.id && firstVideo.thumbnail_url) {
                        memory.thumbnail_url = firstVideo.thumbnail_url;
                        console.log('从第一个视频设置记忆缩略图');
                      } else if (firstVideo && firstVideo.id) {
                        // 如果视频没有缩略图，尝试获取
                        memory.thumbnail_url = await getMemoryVideoThumbnailUrl(memory, firstVideo);
                        console.log('获取并设置视频缩略图');
                      }
                    }
                  } catch (error) {
                    console.error('获取视频缩略图失败:', error);
                  }
                }

                // 确保所有视频都有缩略图
                if (memory.videos && memory.videos.length > 0) {
                  for (const video of memory.videos) {
                    if (!video.thumbnail_url && video.id) {
                      try {
                        video.thumbnail_url = await getMemoryVideoThumbnailUrl(memory, video);
                      } catch (error) {
                        console.error('获取视频缩略图失败:', error);
                      }
                    }
                  }
                }

                return memory;
              }));

              loadedMemories = processedMemories;
              console.log(`成功加载 ${loadedMemories.length} 条记忆`);

              // 获取用户元数据中的分类
              const metadata = await ossStorageService.getUserMetadata();
              if (metadata && metadata.categories && metadata.categories.length > 0) {
                console.log('从元数据中加载分类:', metadata.categories);
                setCategories(metadata.categories);
              }

              message.destroy();
              message.success(`已加载 ${loadedMemories.length} 条记忆`);
            } else {
              console.log('没有找到记忆，尝试从本地存储加载...');
              loadedMemories = await loadFromLocalStorage();
            }
          } catch (ossError) {
            message.destroy();
            console.error('OSS加载失败:', ossError);
            message.error('OSS加载失败，将从本地存储加载');
            // 如果OSS加载失败，回退到本地存储
            loadedMemories = await loadFromLocalStorage();
          }
        } else {
          message.destroy();
          console.log('OSS存储服务初始化失败，将从本地存储加载');
          message.warning('OSS存储服务初始化失败，将从本地存储加载');
          // 如果OSS初始化失败，回退到本地存储
          loadedMemories = await loadFromLocalStorage();
        }

        // 设置记忆并提取标签
        setMemories(loadedMemories);

        // 提取所有标签
        const tags = new Set();
        loadedMemories.forEach(memory => {
          if (memory.tags && Array.isArray(memory.tags)) {
            memory.tags.forEach(tag => tags.add(tag));
          }
        });
        setAllTags(Array.from(tags));

      } catch (error) {
        message.destroy();
        console.error('加载数据失败:', error);
        message.error('加载数据失败，将从本地存储加载');
        // 如果出错，回退到本地存储
        await loadFromLocalStorage();
      } finally {
        setLoading(false);
      }
    };

    // 从本地存储加载记忆
    const loadFromLocalStorage = async () => {
      try {
        // 从本地存储加载记忆
        const loadedMemories = await storageService.getMemories();
        console.log('从本地存储加载记忆:', loadedMemories.length);
        setMemories(loadedMemories);

        // 提取所有标签
        const tags = new Set();
        loadedMemories.forEach(memory => {
          if (memory.tags && Array.isArray(memory.tags)) {
            memory.tags.forEach(tag => tags.add(tag));
          }
        });
        setAllTags(Array.from(tags));

        return loadedMemories;
      } catch (error) {
        console.error('从本地存储加载失败:', error);
        message.error('加载记忆失败');
        return [];
      }
    };

    loadData();
  }, []);

  const goBack = () => {
    // 如果在iframe中，则发送消息到父窗口
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'navigate',
        data: { page: 'add-memory' }
      }, '*');
    } else {
      window.close();
    }
  };

  // 根据分类ID获取分类对象
  const getCategoryById = (categoryId) => {
    return categories.find(cat => cat.id === categoryId) || null;
  };

  // 渲染分类标签
  const renderCategoryTag = (categoryId) => {
    const category = getCategoryById(categoryId);
    if (!category) {
      // 如果找不到分类，显示一个默认标签
      return (
        <Tag color="#ccc">
          <TagOutlined /> 未分类
        </Tag>
      );
    }

    return (
      <Tag color={category.color}>
        {renderIcon(category.icon)} {category.name}
      </Tag>
    );
  };

  // 搜索和筛选记忆
  const [filteredMemories, setFilteredMemories] = useState([]);

  // 当搜索文本、分类或标签变化时更新筛选结果
  useEffect(() => {
    const updateFilteredMemories = async () => {
      console.log('开始更新筛选记忆，当前记忆数量:', memories.length);

      // 如果没有搜索文本，使用普通的筛选
      if (searchText === '') {
        const filtered = memories.filter(memory => {
          // 检查记忆对象是否有效
          if (!memory || typeof memory !== 'object') {
            console.error('无效的记忆对象:', memory);
            return false;
          }

          // 根据分类筛选
          const matchesCategory = selectedCategory === 'all' || memory.category === selectedCategory;

          // 根据标签筛选
          const matchesTag = selectedTag === 'all' ||
            (memory.tags && Array.isArray(memory.tags) && memory.tags.includes(selectedTag));

          return matchesCategory && matchesTag;
        }).sort((a, b) => {
          // 按创建时间降序排序（最新的在前）
          const dateA = new Date(a.created_at || a.createdAt);
          const dateB = new Date(b.created_at || b.createdAt);
          return dateB - dateA;
        });

        console.log('筛选后的记忆数量:', filtered.length);
        setFilteredMemories(filtered);
      } else {
        // 如果有搜索文本，先使用全文搜索索引查找相关记忆
        console.log('执行搜索:', searchText);
        const results = await searchMemories(searchText);
        console.log('搜索结果数量:', results.length);

        // 然后再应用分类和标签筛选
        const filtered = results.filter(memory => {
          // 检查记忆对象是否有效
          if (!memory || typeof memory !== 'object') {
            console.error('无效的记忆对象:', memory);
            return false;
          }

          // 根据分类筛选
          const matchesCategory = selectedCategory === 'all' || memory.category === selectedCategory;

          // 根据标签筛选
          const matchesTag = selectedTag === 'all' ||
            (memory.tags && Array.isArray(memory.tags) && memory.tags.includes(selectedTag));

          return matchesCategory && matchesTag;
        });

        console.log('筛选后的搜索结果数量:', filtered.length);
        setFilteredMemories(filtered);
      }
    };

    updateFilteredMemories();
  }, [searchText, selectedCategory, selectedTag, memories]);

  // 获取筛选后的记忆
  const getFilteredMemories = () => {
    return filteredMemories;
  };

  // 使用搜索索引搜索记忆
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  // 执行搜索
  const searchMemories = async (query) => {
    if (!query) return [];

    // 如果搜索服务未初始化，使用本地搜索
    if (!ossStorageService.isInitialized) {
      const localResults = memories.filter(memory => {
        return memory.title.toLowerCase().includes(query.toLowerCase()) ||
          memory.content.toLowerCase().includes(query.toLowerCase());
      }).sort((a, b) => {
        const dateA = new Date(a.created_at || a.createdAt);
        const dateB = new Date(b.created_at || b.createdAt);
        return dateB - dateA;
      });

      // 处理本地搜索结果的图片和视频
      await Promise.all(localResults.map(async (memory) => {
        // 处理图片缩略图
        if (memory.images && memory.images.length > 0) {
          await Promise.all(memory.images.map(async (image) => {
            if (!image.thumbnail_base64 && image.id) {
              try {
                image.thumbnail_base64 = await getMemoryImageBase64(memory, image);
              } catch (imgError) {
                console.error(`获取图片缩略图失败: ${imgError.message}`);
              }
            }
          }));
        }

        // 处理视频缩略图
        if (memory.videos && memory.videos.length > 0) {
          await Promise.all(memory.videos.map(async (video) => {
            if (!video.thumbnail_base64 && video.id) {
              try {
                video.thumbnail_base64 = await getMemoryVideoBase64(memory, video);
              } catch (videoError) {
                console.error(`获取视频缩略图失败: ${videoError.message}`);
              }
            }
          }));
        }
      }));

      return localResults;
    }

    setIsSearching(true);
    try {
      // 使用搜索索引服务执行搜索
      const results = await searchIndexService.search(query, { limit: 100, fuzzy: true });

      // 将搜索结果转换为记忆对象
      const memoryResults = await Promise.all(
        results.map(async (result) => {
          try {
            // 获取完整记忆
            const memory = await ossStorageService.getMemory(result.id);

            // 处理图片缩略图
            if (memory.images && memory.images.length > 0) {
              await Promise.all(memory.images.map(async (image) => {
                if (!image.thumbnail_url && image.id) {
                  try {
                    image.thumbnail_url = await getMemoryImageUrl(memory, image);
                  } catch (imgError) {
                    console.error(`获取图片缩略图失败: ${imgError.message}`);
                  }
                }
              }));
            }

            // 处理视频缩略图
            if (memory.videos && memory.videos.length > 0) {
              await Promise.all(memory.videos.map(async (video) => {
                if (!video.thumbnail_url && video.id) {
                  try {
                    video.thumbnail_url = await getMemoryVideoThumbnailUrl(memory, video);
                  } catch (videoError) {
                    console.error(`获取视频缩略图失败: ${videoError.message}`);
                  }
                }
              }));
            }

            // 如果记忆没有缩略图，尝试从第一张图片获取
            if (!memory.thumbnail_url && memory.has_images) {
              if (memory.images && memory.images.length > 0) {
                const firstImage = memory.images[0];
                if (firstImage && firstImage.thumbnail_url) {
                  memory.thumbnail_url = firstImage.thumbnail_url;
                } else if (firstImage && firstImage.id) {
                  try {
                    memory.thumbnail_url = await getMemoryImageUrl(memory, firstImage);
                  } catch (thumbError) {
                    console.error(`获取记忆缩略图失败: ${thumbError.message}`);
                  }
                }
              }
            }

            // 添加搜索相关性分数
            return { ...memory, searchScore: result.score };
          } catch (error) {
            console.error(`获取记忆 ${result.id} 失败:`, error);
            return null;
          }
        })
      );

      // 过滤掉空值并按相关性排序
      const filteredResults = memoryResults
        .filter(memory => memory !== null)
        .sort((a, b) => b.searchScore - a.searchScore);

      setSearchResults(filteredResults);
      return filteredResults;
    } catch (error) {
      console.error('搜索失败:', error);
      // 如果搜索失败，回退到本地搜索
      const localResults = memories.filter(memory => {
        return memory.title.toLowerCase().includes(query.toLowerCase()) ||
          memory.content.toLowerCase().includes(query.toLowerCase());
      }).sort((a, b) => {
        return new Date(b.createdAt) - new Date(a.createdAt);
      });

      // 处理本地搜索结果的图片和视频
      await Promise.all(localResults.map(async (memory) => {
        // 处理图片缩略图
        if (memory.images && memory.images.length > 0) {
          await Promise.all(memory.images.map(async (image) => {
            if (!image.thumbnail_base64 && image.id) {
              try {
                image.thumbnail_base64 = await getMemoryImageBase64(memory, image);
              } catch (imgError) {
                console.error(`获取图片缩略图失败: ${imgError.message}`);
              }
            }
          }));
        }

        // 处理视频缩略图
        if (memory.videos && memory.videos.length > 0) {
          await Promise.all(memory.videos.map(async (video) => {
            if (!video.thumbnail_base64 && video.id) {
              try {
                video.thumbnail_base64 = await getMemoryVideoBase64(memory, video);
              } catch (videoError) {
                console.error(`获取视频缩略图失败: ${videoError.message}`);
              }
            }
          }));
        }
      }));

      setSearchResults(localResults);
      return localResults;
    } finally {
      setIsSearching(false);
    }
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    // 重置分页
    setCurrentPage(1);
    setDisplayedMemories([]);
  };

  // 处理分类变化
  const handleCategoryChange = (value) => {
    setSelectedCategory(value);
  };

  // 处理标签变化
  const handleTagChange = (value) => {
    setSelectedTag(value);
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '未知日期';

    try {
      // 处理ISO格式的日期字符串，如 "2025-04-15T15:26:03.023Z"
      const date = new Date(dateString);

      // 检查是否为有效日期
      if (isNaN(date.getTime())) {
        return '无效日期';
      }

      // 格式化日期和时间
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      };

      return date.toLocaleString('zh-CN', options);
    } catch (error) {
      console.error('日期格式化错误:', error, dateString);
      return '日期格式错误';
    }
  };

  // 删除记忆
  const deleteMemory = async (memoryId) => {
    try {
      // 显示加载中提示
      message.loading('正在删除记忆...', 0);

      // 尝试使用OSS存储服务删除记忆
      if (ossStorageService.isInitialized) {
        try {
          // 使用OSS存储服务删除记忆
          const success = await ossStorageService.deleteMemory(memoryId);

          if (success) {
            // 更新本地状态
            const updatedMemories = memories.filter(memory => memory.id !== memoryId);
            setMemories(updatedMemories);

            // 关闭加载中提示
            message.destroy();
            message.success('记忆已删除');
            return;
          } else {
            // 如果返回 false，说明记忆不存在
            console.warn('记忆不存在，尝试从本地存储删除');
            // 继续尝试从本地存储删除
          }
        } catch (ossError) {
          console.error('OSS删除记忆失败:', ossError);
          // 如果OSS删除失败，回退到本地存储
        }
      }

      // 使用本地存储服务删除记忆
      await storageService.deleteMemory(memoryId);

      // 更新本地状态
      const updatedMemories = memories.filter(memory => memory.id !== memoryId);
      setMemories(updatedMemories);

      // 关闭加载中提示
      message.destroy();
      message.success('记忆已删除');
    } catch (error) {
      console.error('删除记忆失败:', error);
      // 关闭加载中提示
      message.destroy();
      message.error('删除记忆失败: ' + error.message);
    }
  };

  // 使用筛选后的记忆列表
  // 注意：filteredMemories 已经作为状态变量定义过了

  // 处理图片预览
  const handleImagePreview = async (memory, image) => {
    try {
      // 尝试获取原始图片
      setPreviewTitle('正在加载...');
      setPreviewImage(image.thumbnail);
      setPreviewVisible(true);

      const originalUrl = await getMemoryImageUrl(memory, image, 'original');
      console.log(originalUrl);
      if (originalUrl) {
        setPreviewImage(originalUrl);
        setPreviewTitle(image.filename || '图片预览');
        console.log("预览成功,原图")
      } else {
        // 如果获取原始图片失败，使用缩略图
        setPreviewImage(image.thumbnail);
        setPreviewTitle(image.filename || '图片预览 (缩略图)');
        console.log("预览成功,缩略图")
      }
    } catch (error) {
      console.error('加载原始图片失败:', error);
      message.error('加载原始图片失败');

      // 如果获取原始图片失败，使用缩略图
      setPreviewImage(image.thumbnail);
      setPreviewTitle(image.filename || '图片预览 (缩略图)');
    }
  };

  // 处理视频播放
  const handleVideoPlay = async (memory, video) => {
    console.log('开始播放视频:', video);
    try {
      // 先设置模态框可见，再加载视频
      setVideoTitle('正在加载视频...');
      setVideoUrl(''); // 清空之前的URL
      setVideoVisible(true);

      // 检查OSS存储服务是否初始化
      if (!ossStorageService.isInitialized) {
        console.log('初始化OSS存储服务...');
        await ossStorageService.initialize();
      }

      if (!ossStorageService.isInitialized) {
        throw new Error('OSS存储服务未初始化');
      }

      console.log('获取视频URL:', memory.id, video.id);
      // 获取视频URL
      const url = await ossStorageService.getVideoUrl(memory.id, video.id, 'original');

      console.log('获取到的视频URL:', url);

      if (url) {
        // 设置视频URL和标题
        setVideoUrl(url);
        setVideoTitle(video.filename || '视频播放');
        console.log('视频准备就绪，可以播放');
      } else {
        throw new Error('获取视频URL失败');
      }
    } catch (error) {
      console.error('加载视频失败:', error);
      message.error('加载视频失败: ' + error.message);
      // 保持模态框可见，显示错误信息
      setVideoTitle('视频加载失败');
    }
  };

  // 关闭图片预览
  const handlePreviewClose = () => {
    setPreviewVisible(false);
  };

  // 关闭视频播放
  const handleVideoClose = () => {
    // 停止视频播放
    if (videoRef.current) {
      videoRef.current.pause();
      videoRef.current.src = '';
    }
    setVideoVisible(false);
    setVideoUrl('');
  };

  // 加载更多记忆
  const loadMoreMemories = () => {
    if (!hasMore || loadingMore) return;

    console.log('加载更多记忆，当前页码:', currentPage);

    // 检查 filteredMemories 是否为数组
    if (!Array.isArray(filteredMemories)) {
      console.error('filteredMemories 不是数组:', filteredMemories);
      setHasMore(false);
      return;
    }

    setLoadingMore(true);

    // 计算下一页的记忆
    const nextPage = currentPage + 1;
    const startIndex = 0;
    const endIndex = nextPage * pageSize;

    console.log('加载范围:', startIndex, endIndex, '当前筛选后记忆数量:', filteredMemories.length);

    // 获取到当前页的所有记忆
    const newDisplayedMemories = filteredMemories.slice(startIndex, endIndex);

    // 异步加载图片URL
    setTimeout(async () => {
      // 处理新加载的记忆的图片和视频URL
      const startProcessingIndex = (currentPage - 1) * pageSize;
      const memoriesToProcess = newDisplayedMemories.slice(startProcessingIndex);

      await Promise.all(memoriesToProcess.map(async (memory) => {
        // 确保记忆有缩略图
        if (!memory.thumbnail_url) {
          // 尝试从第一张图片获取
          if (memory.images && memory.images.length > 0) {
            const firstImage = memory.images[0];
            if (firstImage && firstImage.id && firstImage.thumbnail_url) {
              memory.thumbnail_url = firstImage.thumbnail_url;
            } else if (firstImage && firstImage.id) {
              try {
                memory.thumbnail_url = await getMemoryImageUrl(memory, firstImage);
              } catch (error) {
                console.error('获取记忆缩略图失败:', error);
              }
            }
          }
          // 如果还是没有，尝试从第一个视频获取
          if (!memory.thumbnail_url && memory.videos && memory.videos.length > 0) {
            const firstVideo = memory.videos[0];
            if (firstVideo && firstVideo.id && firstVideo.thumbnail_url) {
              memory.thumbnail_url = firstVideo.thumbnail_url;
            } else if (firstVideo && firstVideo.id) {
              try {
                memory.thumbnail_url = await getMemoryVideoThumbnailUrl(memory, firstVideo);
              } catch (error) {
                console.error('获取记忆视频缩略图失败:', error);
              }
            }
          }
        }

        // 确保所有图片都有缩略图
        if (memory.images && memory.images.length > 0) {
          for (const image of memory.images) {
            if (!image.thumbnail_url && image.id) {
              try {
                image.thumbnail_url = await getMemoryImageUrl(memory, image);
              } catch (error) {
                console.error('获取图片缩略图失败:', error);
              }
            }
          }
        }

        // 确保所有视频都有缩略图
        if (memory.videos && memory.videos.length > 0) {
          for (const video of memory.videos) {
            if (!video.thumbnail_url && video.id) {
              try {
                video.thumbnail_url = await getMemoryVideoThumbnailUrl(memory, video);
              } catch (error) {
                console.error('获取视频缩略图失败:', error);
              }
            }
          }
        }

        return memory;
      }));

      setDisplayedMemories(newDisplayedMemories);
      setCurrentPage(nextPage);
      setHasMore(endIndex < filteredMemories.length);
      setLoadingMore(false);
    }, 300); // 小延迟以显示加载状态
  };

  // 监听筛选条件变化，重置分页
  useEffect(() => {
    console.log('筛选条件变化，重置分页，当前筛选后记忆数量:', filteredMemories.length);
    setCurrentPage(1);

    // 检查 filteredMemories 是否为数组
    if (!Array.isArray(filteredMemories)) {
      console.error('filteredMemories 不是数组:', filteredMemories);
      setDisplayedMemories([]);
      setHasMore(false);
      return;
    }

    const initialMemories = filteredMemories.slice(0, pageSize);
    console.log('初始化显示记忆数量:', initialMemories.length);
    setDisplayedMemories(initialMemories);
    setHasMore(filteredMemories.length > pageSize);
  }, [filteredMemories]);

  // 初始加载第一页
  useEffect(() => {
    if (!loading && Array.isArray(filteredMemories) && filteredMemories.length > 0 && displayedMemories.length === 0) {
      console.log('初始加载第一页，当前筛选后记忆数量:', filteredMemories.length);
      const initialMemories = filteredMemories.slice(0, pageSize);
      console.log('初始化显示记忆数量:', initialMemories.length);
      setDisplayedMemories(initialMemories);
      setHasMore(filteredMemories.length > pageSize);
    }
  }, [loading, filteredMemories, displayedMemories, pageSize]);

  // 添加滚动监听器，实现无限滚动
  useEffect(() => {
    const handleScroll = () => {
      // 如果没有更多记忆或正在加载，则不处理
      if (!hasMore || loadingMore || loading) return;

      // 检查是否滚动到距离底部200px的位置
      const scrollTop = window.scrollY || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      if (scrollTop + windowHeight >= documentHeight - 200) {
        loadMoreMemories();
      }
    };

    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll);

    // 组件卸载时移除监听器
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [hasMore, loadingMore, loading, filteredMemories]);

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={goBack}
          style={{ marginRight: '16px' }}
        />
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
          <Title level={3} style={{ margin: '0' }}>浏览记忆</Title>
        </div>
      </Header>
      <Content style={{ padding: '24px' }}>
        <Space direction="vertical" style={{ width: '100%', marginBottom: '24px' }}>
          <Search
            placeholder="搜索记忆标题或内容"
            allowClear
            enterButton={isSearching ? <Spin size="small" /> : <SearchOutlined />}
            size="large"
            onSearch={handleSearch}
            loading={isSearching}
          />

          <div style={{ display: 'flex', marginTop: '16px' }}>
            <div style={{ marginRight: '16px', flex: 1 }}>
              <span style={{ marginRight: '8px' }}>分类：</span>
              <Select
                style={{ width: '100%' }}
                value={selectedCategory}
                onChange={handleCategoryChange}
              >
                <Select.Option value="all">所有分类</Select.Option>
                {categories.map(category => (
                  <Select.Option key={category.id} value={category.id}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <div style={{
                        width: '16px',
                        height: '16px',
                        borderRadius: '50%',
                        backgroundColor: category.color,
                        marginRight: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        fontSize: '10px'
                      }}>
                        {renderIcon(category.icon)}
                      </div>
                      {category.name}
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </div>

            <div style={{ flex: 1 }}>
              <span style={{ marginRight: '8px' }}>标签：</span>
              <Select
                style={{ width: '100%' }}
                value={selectedTag}
                onChange={handleTagChange}
              >
                <Select.Option value="all">所有标签</Select.Option>
                {allTags.map(tag => (
                  <Select.Option key={tag} value={tag}>
                    {tag}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
        </Space>

        <Divider orientation="left">记忆列表 ({filteredMemories.length})</Divider>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>加载中...</div>
        ) : filteredMemories.length === 0 ? (
          <Empty description="没有找到记忆" />
        ) : (
          <div>
            <List
              grid={{ gutter: 16, xs: 1, sm: 1, md: 1, lg: 2, xl: 2, xxl: 3 }}
              dataSource={displayedMemories}
              renderItem={memory => (
              <List.Item>
                <Card
                  title={memory.title}
                  extra={renderCategoryTag(memory.category)}
                  actions={[
                    <Popconfirm
                      title="确定要删除这条记忆吗？"
                      description="删除后将无法恢复"
                      onConfirm={() => deleteMemory(memory.id)}
                      okText="删除"
                      cancelText="取消"
                      okButtonProps={{ danger: true }}
                    >
                      <Button icon={<DeleteOutlined />} danger size="small">删除</Button>
                    </Popconfirm>
                  ]}
                  bodyStyle={{ padding: '16px', textAlign: 'left' }}
                >
                  <div style={{ marginBottom: '16px', textAlign: 'left' }}>
                    <div style={{ whiteSpace: 'pre-wrap', marginBottom: '16px', textAlign: 'left' }}>{memory.content}</div>

                    {/* 显示图片和视频的九宫格 */}
                    <div style={{ marginBottom: '16px', textAlign: 'left' }}>
                      {/* 合并图片和视频并限制最多显示9个 */}
                      {(() => {
                        // 准备所有媒体文件
                        const mediaItems = [];

                        // 添加图片
                        if (memory.images && memory.images.length > 0) {
                          memory.images.forEach(image => {
                            if (image.thumbnail_url) {
                              mediaItems.push({
                                id: image.id,
                                type: 'image',
                                thumbnail: image.thumbnail_url,
                                filename: image.filename,
                                data: image
                              });
                            }
                          });
                        }

                        // 添加视频
                        if (memory.videos && memory.videos.length > 0) {
                          memory.videos.forEach(video => {
                            if (video.thumbnail_url) {
                              mediaItems.push({
                                id: video.id,
                                type: 'video',
                                thumbnail: video.thumbnail_url,
                                filename: video.filename,
                                data: video
                              });
                            }
                          });
                        }

                        // 如果没有媒体文件，但有记忆缩略图
                        if (mediaItems.length === 0 && memory.thumbnail_url) {
                          mediaItems.push({
                            id: 'memory-thumbnail',
                            type: 'image',
                            thumbnail: memory.thumbnail_url,
                            filename: '记忆缩略图',
                            data: { thumbnail_url: memory.thumbnail_url }
                          });
                        }

                        // 如果没有媒体文件，返回null
                        if (mediaItems.length === 0) {
                          return null;
                        }

                        // 限制最多显示9个
                        const displayItems = mediaItems.slice(0, 9);
                        const totalItems = mediaItems.length;

                        // 计算九宫格布局
                        let gridTemplateColumns = '1fr 1fr 1fr'; // 默认三列
                        let gridWidth = '100%'; // 默认使用100%宽度，与文字内容保持一致
                        let containerStyle = {};

                        // 根据图片数量动态调整布局
                        if (displayItems.length === 1) {
                          // 单张图片，宽度为文字宽度的1/3
                          containerStyle = {
                            width: '33.33%',
                            marginRight: '66.67%'
                          };
                        } else if (displayItems.length === 2) {
                          // 两张图片，每张宽度为文字宽度的1/3，总宽度为2/3
                          containerStyle = {
                            width: '66.67%',
                            marginRight: '33.33%'
                          };
                        } else if (displayItems.length >= 3 && displayItems.length <= 9) {
                          // 3-9张图片，使用全宽三列布局
                          containerStyle = {
                            width: '100%'
                          };
                        }

                        return (
                          <div style={{
                            display: 'grid',
                            gridTemplateColumns: gridTemplateColumns,
                            gap: '4px',
                            marginBottom: '8px',
                            marginLeft: '0', // 左对齐
                            ...containerStyle // 应用根据图片数量计算的样式
                          }}>
                            {displayItems.map((item, index) => (
                              <div
                                key={item.id}
                                style={{
                                  position: 'relative',
                                  paddingBottom: '100%', // 1:1 的宽高比
                                  overflow: 'hidden',
                                  borderRadius: '4px',
                                  boxShadow: '0 1px 3px rgba(0,0,0,0.1)', // 添加轻微阴影
                                  transition: 'all 0.3s ease', // 添加过渡效果
                                  width: '100%', // 使用100%宽度以适应网格布局
                                  height: 'auto' // 高度自适应
                                }}
                                onMouseEnter={(e) => {
                                  e.currentTarget.style.transform = 'scale(1.02)';
                                  e.currentTarget.style.boxShadow = '0 2px 5px rgba(0,0,0,0.15)';
                                }}
                                onMouseLeave={(e) => {
                                  e.currentTarget.style.transform = 'scale(1)';
                                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
                                }}
                              >
                                <img
                                  src={item.thumbnail}
                                  alt={item.filename}
                                  style={{
                                    position: 'absolute',
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                    cursor: 'pointer',
                                    transition: 'transform 0.3s ease', // 添加过渡效果
                                    borderRadius: '4px' // 保持圆角
                                  }}
                                  onClick={() => item.type === 'image'
                                    ? handleImagePreview(memory, item.data)
                                    : handleVideoPlay(memory, item.data)
                                  }
                                />

                                {/* 如果是视频，显示播放图标和视频标识 */}
                                {item.type === 'video' && (
                                  <>
                                    {/* 视频标识 - 右上角 */}
                                    <div style={{
                                      position: 'absolute',
                                      top: '5px',
                                      right: '5px',
                                      backgroundColor: 'rgba(0,0,0,0.6)',
                                      color: 'white',
                                      padding: '2px 5px',
                                      borderRadius: '3px',
                                      fontSize: '10px',
                                      fontWeight: 'bold',
                                      zIndex: 10,
                                      opacity: '0.8',
                                      transition: 'opacity 0.3s ease'
                                    }}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.opacity = '1';
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.opacity = '0.8';
                                    }}
                                    >
                                      <VideoCameraOutlined style={{ marginRight: '2px' }} /> 视频
                                    </div>

                                    {/* 播放按钮 - 中间 */}
                                    <div
                                      style={{
                                        position: 'absolute',
                                        top: '50%',
                                        left: '50%',
                                        transform: 'translate(-50%, -50%)',
                                        color: 'white',
                                        fontSize: '24px',
                                        textShadow: '0 0 4px rgba(0,0,0,0.5)',
                                        backgroundColor: 'rgba(0,0,0,0.3)',
                                        borderRadius: '50%',
                                        width: '36px',
                                        height: '36px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        transition: 'all 0.3s ease', // 添加过渡效果
                                        opacity: 0.8, // 默认稍微透明
                                        boxShadow: '0 0 10px rgba(0,0,0,0.3)', // 添加阴影
                                        cursor: 'pointer', // 添加手型光标
                                        zIndex: 10 // 确保在图片上层
                                      }}
                                      onClick={() => handleVideoPlay(memory, item.data)} // 添加点击事件
                                      onMouseEnter={(e) => {
                                        e.currentTarget.style.opacity = '1';
                                        e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1.1)';
                                        e.currentTarget.style.boxShadow = '0 0 15px rgba(0,0,0,0.5)';
                                      }}
                                      onMouseLeave={(e) => {
                                        e.currentTarget.style.opacity = '0.8';
                                        e.currentTarget.style.transform = 'translate(-50%, -50%) scale(1)';
                                        e.currentTarget.style.boxShadow = '0 0 10px rgba(0,0,0,0.3)';
                                      }}
                                    >
                                      <PlayCircleOutlined />
                                    </div>
                                  </>
                                )}

                                {/* 如果是最后一个并且还有更多图片，显示+N */}
                                {index === displayItems.length - 1 && totalItems > 9 && (
                                  <div style={{
                                    position: 'absolute',
                                    top: 0,
                                    left: 0,
                                    width: '100%',
                                    height: '100%',
                                    backgroundColor: 'rgba(0,0,0,0.6)',
                                    color: 'white',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '28px',
                                    fontWeight: 'bold',
                                    borderRadius: '4px', // 保持圆角
                                    cursor: 'pointer',
                                    transition: 'all 0.3s ease', // 添加过渡效果
                                    backdropFilter: 'blur(2px)' // 模糊背景，增强效果
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.backgroundColor = 'rgba(0,0,0,0.7)';
                                    e.currentTarget.style.fontSize = '32px';
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.backgroundColor = 'rgba(0,0,0,0.6)';
                                    e.currentTarget.style.fontSize = '28px';
                                  }}
                                  >
                                    +{totalItems - 8}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        );
                      })()}
                    </div>

                    <div>
                      {memory.tags && memory.tags.map(tag => (
                        <Tag key={tag} style={{ marginBottom: '4px' }}>{tag}</Tag>
                      ))}
                    </div>
                  </div>
                  <div style={{ fontSize: '12px', color: '#888', display: 'flex', alignItems: 'center' }}>
                    <ClockCircleOutlined style={{ marginRight: '4px' }} />
                    {formatDate(memory.created_at)}
                  </div>
                </Card>
              </List.Item>
            )}
            />

            {/* 加载更多按钮 */}
            {hasMore && (
              <div style={{ textAlign: 'center', marginTop: '20px', marginBottom: '20px' }}>
                <Button
                  type="primary"
                  onClick={loadMoreMemories}
                  loading={loadingMore}
                >
                  {loadingMore ? '正在加载更多...' : '加载更多记忆'}
                </Button>
              </div>
            )}

            {/* 底部状态提示 */}
            {!hasMore && displayedMemories.length > 0 && (
              <div style={{ textAlign: 'center', color: '#999', padding: '20px 0' }}>
                已加载全部 {filteredMemories.length} 条记忆
              </div>
            )}
          </div>
        )}
      </Content>
      <Footer style={{ textAlign: 'center' }}>
        拾光忆栈 ©{new Date().getFullYear()}
      </Footer>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={handlePreviewClose}
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ padding: '0', textAlign: 'center', maxHeight: '80vh', overflow: 'auto' }}
      >
        <img
          alt={previewTitle}
          style={{ maxWidth: '100%', maxHeight: '70vh' }}
          src={previewImage}
        />
      </Modal>

      {/* 视频播放模态框 */}
      <Modal
        open={videoVisible}
        title={videoTitle}
        footer={null}
        onCancel={handleVideoClose}
        width="80%"
        style={{ top: 20 }}
        bodyStyle={{ padding: '0', textAlign: 'center', maxHeight: '80vh', overflow: 'auto' }}
        destroyOnClose={true}
      >
        {videoUrl ? (
          <video
            ref={videoRef}
            controls
            autoPlay
            style={{ maxWidth: '100%', maxHeight: '70vh' }}
            src={videoUrl}
            onError={(e) => {
              if (videoVisible) {
                console.error('视频加载错误:', e);
                // message.error('视频加载错误，请检查网络或视频格式');
                setVideoTitle('视频加载错误');
              }
            }}
          />
        ) : (
          <div style={{ padding: '40px', textAlign: 'center' }}>
            <Spin size="large" />
            <div style={{ marginTop: '20px' }}>正在加载视频，请稍候...</div>
          </div>
        )}
      </Modal>
    </Layout>
  );
};

export default BrowseMemories;
