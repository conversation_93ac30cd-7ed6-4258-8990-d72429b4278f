/**
 * OssStorageService - OSS存储服务类
 *
 * 用于管理记忆内容在OSS中的存储，包括文本、图片和视频
 */
import { storageManager, StorageTypes } from './storage';
import storageService from './StorageService';
import searchIndexService from './SearchIndexService';

class OssStorageService {
  constructor() {
    this.provider = null;
    this.isInitialized = false;
    this.userId = null;
    this.bucketName = null;
    this.baseUrl = null;
  }

  /**
   * 初始化OSS存储服务
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      // 获取云存储设置
      const settings = await storageService.getSettings();
      console.log('云存储设置:', settings);
      const providerName = settings.storageProvider;
      let config = null;
      if (providerName === StorageTypes.HUAWEI_OBS) {
        // 初始化华为云OBS存储提供者
        config = settings.huaweiObs;
       await storageManager.registerProvider(StorageTypes.HUAWEI_OBS, StorageTypes.HUAWEI_OBS, config, false);
      } else if (providerName === StorageTypes.MINIO) {
        // 初始化MinIO存储提供者
        config = settings.minio;
        await storageManager.registerProvider(StorageTypes.MINIO, StorageTypes.MINIO, config, false);
      } else {
        console.error('未知的存储提供者：', providerName);
        return false;
      }

      console.log("providerName:", providerName)
      console.log("getProviderNames:", storageManager.getProviderNames())
      this.provider = storageManager.getProvider(providerName);
      this.bucketName = this.provider.getBucketName();

      // 根据不同的提供者设置基础URL
      if (providerName === StorageTypes.HUAWEI_OBS) {
        this.baseUrl = `https://${this.bucketName}.${config.endpoint}`;
      } else if (providerName === StorageTypes.MINIO) {
        const protocol = config.useSSL !== undefined ? (config.useSSL ? 'https' : 'http') : 'https';
        const port = config.port ? `:${config.port}` : '';
        this.baseUrl = `${protocol}://${config.endPoint}${port}/${this.bucketName}`;
      } else {
        this.baseUrl = '';
      }

      // 获取或生成用户ID
      this.userId = await this._getUserId();

      this.isInitialized = true;
      console.log('OSS存储服务初始化成功');

      // 初始化搜索索引服务
      searchIndexService.initialize(this, storageService.localCache);
      console.log('搜索索引服务初始化成功');

      return true;
    } catch (error) {
      console.error('初始化OSS存储服务失败:', error);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * 获取用户ID，如果不存在则生成一个
   * @returns {Promise<string>} 用户ID
   */
  async _getUserId() {
    const settings = await storageService.getSettings();
    if (settings.userId) {
      return settings.userId;
    }

    // 生成新的用户ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 保存用户ID
    await storageService.updateSetting('userId', userId);

    // 初始化用户元数据
    await this._initUserMetadata(userId);

    return userId;
  }

  /**
   * 初始化用户元数据
   * @param {string} userId - 用户ID
   * @returns {Promise<void>}
   */
  async _initUserMetadata(userId) {
    // 使用_createDefaultMetadata创建元数据
    const metadata = await this._createDefaultMetadata();
    metadata.user_id = userId;

    // 保存元数据到OSS
    await this.putObject(`users/${userId}/metadata.json`, JSON.stringify(metadata));

    // 保存到本地缓存
    await storageService.updateSetting('ossMetadata', metadata);
  }

  /**
   * 获取用户元数据
   * @returns {Promise<Object>} 用户元数据
   */
  // 缓存元数据和最后获取时间
  _cachedMetadata = null;
  _lastMetadataFetch = 0;
  _isGettingMetadata = false; // 添加标志防止递归调用

  async getUserMetadata(forceSync = false) {
    // 如果已经在获取元数据中，防止递归调用
    if (this._isGettingMetadata) {
      console.log('检测到递归调用getUserMetadata，返回缓存或空对象');
      return this._cachedMetadata || {
        user_id: this.userId || 'default_user',
        categories: [],
        memory_index: { recent: [], favorites: [], by_category: {}, by_tag: {} },
        memory_metadata: {}
      };
    }

    // 设置标志，表示正在获取元数据
    this._isGettingMetadata = true;

    try {
      // 如果有缓存并且不超过10秒，且不是强制同步，直接返回缓存
      const now = Date.now();
      if (!forceSync && this._cachedMetadata && (now - this._lastMetadataFetch < 10000)) {
        console.log('使用内存缓存的元数据');
        return this._cachedMetadata;
      }

      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.isInitialized) {
        throw new Error('OSS存储服务未初始化');
      }

      // 先尝试从本地缓存获取
      const settings = await storageService.getSettings();
      const cachedMetadata = settings.ossMetadata;

      // 检查缓存是否过期或强制同步
      if (!forceSync && cachedMetadata && !this._isMetadataExpired(cachedMetadata)) {
        // 更新内存缓存
        this._cachedMetadata = cachedMetadata;
        this._lastMetadataFetch = now;
        return cachedMetadata;
      }

      try {
        // 尝试从OSS获取元数据
        console.log(`从OSS获取元数据: users/${this.userId}/metadata.json`);
        const metadata = await this.getObject(`users/${this.userId}/metadata.json`);

        // 检查元数据中的分类与本地分类是否一致
        const localCategories = settings.categories || [];
        const obsCategories = metadata.categories || [];

        // 检查分类数量是否一致
        const categoriesMatch = localCategories.length === obsCategories.length &&
          // 检查每个分类的ID是否存在
          localCategories.every(localCat =>
            obsCategories.some(obsCat => obsCat.id === localCat.id));

        if (!categoriesMatch && !forceSync) { // 添加forceSync检查，防止递归
          console.log('本地分类与OBS分类不一致，需要同步');
          console.log('本地分类:', localCategories);
          console.log('OBS分类:', obsCategories);

          // 如果本地分类更新时间更新，使用本地分类
          const localLastUpdated = settings.categoriesLastUpdated || 0;
          const obsLastUpdated = new Date(metadata.last_sync || 0).getTime();

          // 手动更新元数据中的分类，而不是调用syncCategories
          if (localLastUpdated > obsLastUpdated) {
            console.log('本地分类更新时间更新，直接更新OBS元数据');

            // 更新元数据中的分类
            metadata.categories = localCategories;

            // 更新分类索引
            const byCategoryIndex = {};
            localCategories.forEach(category => {
              // 保留现有的分类索引，如果不存在则创建新的
              byCategoryIndex[category.id] = metadata.memory_index.by_category[category.id] || [];
            });
            metadata.memory_index.by_category = byCategoryIndex;

            // 保存更新后的元数据到OBS
            await this.putObject(`users/${this.userId}/metadata.json`, JSON.stringify(metadata));

            // 更新本地缓存
            await storageService.updateSetting('ossMetadata', metadata);
          } else {
            console.log('OBS分类更新时间更新，直接更新本地设置');

            // 更新本地设置中的分类
            if (obsCategories && obsCategories.length > 0) {
              settings.categories = obsCategories;
              await storageService.saveSettings(settings);
            }

            // 更新本地缓存
            await storageService.updateSetting('ossMetadata', metadata);
          }
        } else {
          // 如果分类一致，直接更新缓存
          await storageService.updateSetting('ossMetadata', metadata);
        }

        // 更新内存缓存
        this._cachedMetadata = metadata;
        this._lastMetadataFetch = now;

        return metadata;
      } catch (ossError) {
        console.log('从OSS获取元数据失败，创建新的元数据:', ossError);

        // 如果元数据不存在，创建新的元数据
        const defaultMetadata = await this._createDefaultMetadata();
        await this.putObject(`users/${this.userId}/metadata.json`, JSON.stringify(defaultMetadata));

        // 更新本地缓存
        await storageService.updateSetting('ossMetadata', defaultMetadata);

        // 更新内存缓存
        this._cachedMetadata = defaultMetadata;
        this._lastMetadataFetch = now;

        return defaultMetadata;
      }
    } catch (error) {
      console.error('获取用户元数据失败:', error);

      // 如果所有尝试都失败，返回默认元数据
      const defaultMetadata = await this._createDefaultMetadata();

      // 更新内存缓存
      this._cachedMetadata = defaultMetadata;
      this._lastMetadataFetch = now;

      return defaultMetadata;
    } finally {
      // 重置标志，表示已完成获取元数据
      this._isGettingMetadata = false;
    }
  }

  /**
   * 创建默认元数据
   * @returns {Object} 默认元数据
   */
  async _createDefaultMetadata() {
    try {
      // 从设置中获取分类
      const settings = await storageService.getSettings();
      const categories = settings.categories || [];

      console.log('从本地设置创建元数据，分类数量:', categories.length);

      // 创建分类索引
      const byCategoryIndex = {};
      categories.forEach(category => {
        byCategoryIndex[category.id] = {
          count: 0,
          file: `indexes/categories/category_${category.id}.json`
        };
      });

      // 确保至少有一个默认分类
      if (categories.length === 0) {
        console.log('本地没有分类设置，使用默认分类');
        const defaultCategories = [
          { id: '1', name: '工作', color: '#1890ff', icon: 'BulbOutlined' },
          { id: '2', name: '生活', color: '#52c41a', icon: 'HeartOutlined' },
          { id: '3', name: '学习', color: '#722ed1', icon: 'BookOutlined' },
          { id: '4', name: '其他', color: '#faad14', icon: 'StarOutlined' },
        ];

        const newByCategoryIndex = {};
        defaultCategories.forEach(category => {
          newByCategoryIndex[category.id] = {
            count: 0,
            file: `indexes/categories/category_${category.id}.json`
          };
        });

        // 更新本地设置
        settings.categories = defaultCategories;
        settings.defaultCategory = '4';
        await storageService.saveSettings(settings);

        // 创建支持分片存储的元数据
        return {
          user_id: this.userId || 'default_user',
          version: 1,
          last_sync: new Date().toISOString(),
          last_modified_by: storageService.getDeviceId(),
          memory_count: 0,
          chunk_count: 0,
          categories: defaultCategories,
          memory_index: {
            recent: [],  // 仅保留最近的100条
            favorites: [],  // 仅保留最近的100条
            by_category: newByCategoryIndex,
            by_tag: {}
          },
          memory_chunks: [],  // 记忆分块信息
          memory_id_to_chunk: {},  // 记忆ID到分块的映射
          devices: {
            [storageService.getDeviceId()]: {
              last_active: new Date().toISOString(),
              device_type: this._getDeviceType(),
              device_name: this._getDeviceName(),
              os: this._getOsInfo(),
              user_agent: navigator.userAgent
            }
          }
        };
      }

      // 创建支持分片存储的元数据
      return {
        user_id: this.userId || 'default_user',
        version: 1,
        last_sync: new Date().toISOString(),
        last_modified_by: storageService.getDeviceId(),
        memory_count: 0,
        chunk_count: 0,
        categories: categories,
        memory_index: {
          recent: [],  // 仅保留最近的100条
          favorites: [],  // 仅保留最近的100条
          by_category: byCategoryIndex,
          by_tag: {}
        },
        memory_chunks: [],  // 记忆分块信息
        memory_id_to_chunk: {},  // 记忆ID到分块的映射
        devices: {
          [storageService.getDeviceId()]: {
            last_active: new Date().toISOString(),
            device_type: this._getDeviceType(),
            device_name: this._getDeviceName(),
            os: this._getOsInfo(),
            user_agent: navigator.userAgent
          }
        }
      };
    } catch (error) {
      console.error('创建默认元数据失败:', error);
      // 如果出错，返回默认分类
      const defaultCategories = [
        { id: '1', name: '工作', color: '#1890ff', icon: 'BulbOutlined' },
        { id: '2', name: '生活', color: '#52c41a', icon: 'HeartOutlined' },
        { id: '3', name: '学习', color: '#722ed1', icon: 'BookOutlined' },
        { id: '4', name: '其他', color: '#faad14', icon: 'StarOutlined' },
      ];

      const byCategoryIndex = {};
      defaultCategories.forEach(category => {
        byCategoryIndex[category.id] = {
          count: 0,
          file: `indexes/categories/category_${category.id}.json`
        };
      });

      // 创建支持分片存储的元数据
      return {
        user_id: this.userId || 'default_user',
        version: 1,
        last_sync: new Date().toISOString(),
        last_modified_by: storageService.getDeviceId(),
        memory_count: 0,
        chunk_count: 0,
        categories: defaultCategories,
        memory_index: {
          recent: [],  // 仅保留最近的100条
          favorites: [],  // 仅保留最近的100条
          by_category: byCategoryIndex,
          by_tag: {}
        },
        memory_chunks: [],  // 记忆分块信息
        memory_id_to_chunk: {},  // 记忆ID到分块的映射
        devices: {
          [storageService.getDeviceId()]: {
            last_active: new Date().toISOString(),
            device_type: this._getDeviceType(),
            device_name: this._getDeviceName(),
            os: this._getOsInfo(),
            user_agent: navigator.userAgent
          }
        }
      };
    }
  }

  /**
   * 获取设备类型
   * @returns {string} 设备类型
   * @private
   */
  _getDeviceType() {
    const deviceId = storageService.getDeviceId();
    if (deviceId.startsWith('firefox_')) {
      return 'firefox';
    } else if (deviceId.startsWith('safari_')) {
      return 'safari';
    } else if (deviceId.startsWith('edge_')) {
      return 'edge';
    } else if (deviceId.startsWith('opera_')) {
      return 'opera';
    }
    return 'chrome';
  }

  /**
   * 获取设备名称
   * @returns {string} 设备名称
   * @private
   */
  _getDeviceName() {
    const deviceType = this._getDeviceType();
    switch (deviceType) {
      case 'firefox': return 'Firefox 浏览器';
      case 'safari': return 'Safari 浏览器';
      case 'edge': return 'Edge 浏览器';
      case 'opera': return 'Opera 浏览器';
      default: return 'Chrome 浏览器';
    }
  }

  /**
   * 获取操作系统信息
   * @returns {string} 操作系统信息
   * @private
   */
  _getOsInfo() {
    if (navigator.userAgent.indexOf('Windows') !== -1) {
      return 'Windows';
    } else if (navigator.userAgent.indexOf('Mac') !== -1) {
      return 'macOS';
    } else if (navigator.userAgent.indexOf('Linux') !== -1) {
      return 'Linux';
    } else if (navigator.userAgent.indexOf('Android') !== -1) {
      return 'Android';
    } else if (navigator.userAgent.indexOf('iOS') !== -1 || navigator.userAgent.indexOf('iPhone') !== -1 || navigator.userAgent.indexOf('iPad') !== -1) {
      return 'iOS';
    }
    return 'Unknown';
  }

  /**
   * 更新用户元数据
   * @param {Object} metadata - 用户元数据
   * @returns {Promise<void>}
   */
  async updateUserMetadata(metadata) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    try {
      // 获取当前元数据
      const currentMetadata = await this.getUserMetadata(true); // 强制从云端获取

      // 检查版本号
      if (metadata.version && currentMetadata.version && metadata.version < currentMetadata.version) {
        console.warn(`元数据版本冲突: 本地版本 ${metadata.version} < 云端版本 ${currentMetadata.version}`);
        throw new Error('元数据已被其他设备更新，请刷新后重试');
      }

      // 更新版本号
      metadata.version = (metadata.version || 0) + 1;
      metadata.last_sync = new Date().toISOString();

      // 记录最后修改的设备
      const deviceId = storageService.getDeviceId();
      metadata.last_modified_by = deviceId;

      // 更新设备信息
      if (!metadata.devices) {
        metadata.devices = {};
      }

      // 检测设备类型
      let deviceType = 'chrome';
      let deviceName = 'Chrome 浏览器';

      if (deviceId.startsWith('firefox_')) {
        deviceType = 'firefox';
        deviceName = 'Firefox 浏览器';
      } else if (deviceId.startsWith('safari_')) {
        deviceType = 'safari';
        deviceName = 'Safari 浏览器';
      } else if (deviceId.startsWith('edge_')) {
        deviceType = 'edge';
        deviceName = 'Edge 浏览器';
      } else if (deviceId.startsWith('opera_')) {
        deviceType = 'opera';
        deviceName = 'Opera 浏览器';
      }

      // 获取操作系统信息
      let osInfo = 'Unknown';
      if (navigator.userAgent.indexOf('Windows') !== -1) {
        osInfo = 'Windows';
      } else if (navigator.userAgent.indexOf('Mac') !== -1) {
        osInfo = 'macOS';
      } else if (navigator.userAgent.indexOf('Linux') !== -1) {
        osInfo = 'Linux';
      } else if (navigator.userAgent.indexOf('Android') !== -1) {
        osInfo = 'Android';
      } else if (navigator.userAgent.indexOf('iOS') !== -1 || navigator.userAgent.indexOf('iPhone') !== -1 || navigator.userAgent.indexOf('iPad') !== -1) {
        osInfo = 'iOS';
      }

      metadata.devices[deviceId] = {
        last_active: new Date().toISOString(),
        device_type: deviceType,
        device_name: deviceName,
        os: osInfo,
        user_agent: navigator.userAgent
      };

      // 保存到OSS
      await this.putObject(`users/${this.userId}/metadata.json`, JSON.stringify(metadata));

      // 更新本地缓存
      await storageService.updateSetting('ossMetadata', metadata);

      // 更新内存缓存
      this._cachedMetadata = metadata;
      this._lastMetadataFetch = Date.now();

      console.log(`元数据更新成功，新版本: ${metadata.version}`);
    } catch (error) {
      console.error('更新元数据失败:', error);
      throw error;
    }
  }

  /**
   * 同步分类设置
   * 确保本地分类设置与OBS上的metadata.json保持一致
   * @param {boolean} localToObs - 是否从本地同步到OBS，默认为true
   * @returns {Promise<void>}
   */
  async syncCategories(localToObs = true) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.isInitialized) {
        throw new Error('OSS存储服务未初始化');
      }

      console.log('开始同步分类设置...');

      // 获取本地设置
      const settings = await storageService.getSettings();

      // 直接从OSS获取元数据，而不是调用getUserMetadata，避免递归
      console.log(`直接从OSS获取元数据: users/${this.userId}/metadata.json`);
      let metadata;
      try {
        metadata = await this.getObject(`users/${this.userId}/metadata.json`);
      } catch (error) {
        console.error('从OSS获取元数据失败，创建新的元数据:', error);
        metadata = await this._createDefaultMetadata();
        await this.putObject(`users/${this.userId}/metadata.json`, JSON.stringify(metadata));
      }

      if (localToObs) {
        // 从本地同步到OBS
        console.log('从本地同步分类到OBS...');
        console.log('本地分类:', settings.categories);
        console.log('OBS分类:', metadata.categories);

        // 更新元数据中的分类
        metadata.categories = settings.categories || [];

        // 更新分类索引
        const byCategoryIndex = {};
        metadata.categories.forEach(category => {
          // 保留现有的分类索引，如果不存在则创建新的
          byCategoryIndex[category.id] = metadata.memory_index.by_category[category.id] || [];
        });
        metadata.memory_index.by_category = byCategoryIndex;

        // 保存更新后的元数据到OBS
        await this.updateUserMetadata(metadata);
        console.log('分类已同步到OBS');
      } else {
        // 从OBS同步到本地
        console.log('从OBS同步分类到本地...');
        console.log('本地分类:', settings.categories);
        console.log('OBS分类:', metadata.categories);

        // 更新本地设置中的分类
        if (metadata.categories && metadata.categories.length > 0) {
          settings.categories = metadata.categories;
          await storageService.saveSettings(settings);
          console.log('分类已同步到本地');
        } else {
          console.log('OBS上没有分类数据，保持本地设置不变');
        }
      }

      return true;
    } catch (error) {
      console.error('同步分类设置失败:', error);
      throw error;
    }
  }

  /**
   * 获取图片的base64编码
   * @param {string} path - 图片路径
   * @returns {Promise<string>} base64编码的图片
   * @private
   */
  async _getImageBase64(path) {
    try {
      // 先检查缓存
      const cacheKey = `base64_${path}`;
      const cachedBase64 = await storageService.localCache.getItem(cacheKey);
      if (cachedBase64) {
        console.log('从缓存获取图片base64:', path);
        return cachedBase64;
      }

      // 获取图片二进制数据
      const fullPath = `users/${this.userId}${path}`;
      console.log(`从云存储下载图片: ${fullPath}`);

      let base64;

      try {
        // 使用抽象接口获取对象
        const response = await this.provider.getObject(fullPath, {
          SaveByType: 'arraybuffer'
        });

        if (response.CommonMsg.Status >= 300) {
          throw new Error(`获取图片失败，状态码: ${response.CommonMsg.Status}`);
        }

      // 直接根据ContentType处理二进制数据
      const contentType = response.InterfaceResult.ContentType || 'image/jpeg';
      const content = response.InterfaceResult.Content;

      console.log(`响应ContentType: ${contentType}`);
      console.log(`响应内容类型: ${typeof content}`);

      // 创建Blob对象
      let blob;

      // 根据ContentType处理不同类型的图片
      switch (contentType) {
        case 'image/jpeg':
        case 'image/png':
        case 'image/gif':
        case 'image/webp':
        case 'image/svg+xml':
        case 'image/bmp':
        case 'image/tiff':
          // 处理图片类型
          console.log(`处理图片类型: ${contentType}`);

          // 将内容转换为Uint8Array
          let uint8Array;

          if (content instanceof Uint8Array) {
            uint8Array = content;
          } else if (content instanceof ArrayBuffer) {
            uint8Array = new Uint8Array(content);
          } else if (typeof content === 'string') {
            // 如果是字符串，尝试将其转换为字节数组
            console.log('内容是字符串，长度:', content.length);

            // 检查是否是二进制字符串（如以PNG开头）
            if (content.startsWith('\x89PNG') || content.startsWith('\xff\xd8\xff') || content.startsWith('GIF')) {
              // 这是二进制数据被错误地解释为字符串
              console.log('检测到二进制图片数据被解释为字符串');

              try {
                // 尝试将字符串转换为字节数组
                const bytes = new Uint8Array(content.length);
                for (let i = 0; i < content.length; i++) {
                  bytes[i] = content.charCodeAt(i) & 0xff;
                }
                uint8Array = bytes;
                console.log(`成功将二进制字符串转换为Uint8Array，长度: ${uint8Array.length}`);
              } catch (error) {
                console.error('转换二进制字符串失败:', error);
                throw new Error('需要使用签名URL方法获取图片');
              }
            } else {
              // 如果是其他字符串，使用签名URL方法
              console.log('内容是普通字符串，使用签名URL方法');
              throw new Error('需要使用签名URL方法获取图片');
            }
          } else {
            console.log('内容类型不支持直接转换，使用签名URL方法');
            throw new Error('需要使用签名URL方法获取图片');
          }

          // 创建Blob对象
          blob = new Blob([uint8Array], { type: contentType });
          console.log(`创建Blob成功，大小: ${blob.size} 字节`);
          break;

        case 'application/octet-stream':
          // 处理通用二进制数据
          console.log('处理通用二进制数据');

          if (content instanceof Uint8Array || content instanceof ArrayBuffer) {
            // 如果是二进制数据，直接创建Blob
            const uint8Array = content instanceof Uint8Array ? content : new Uint8Array(content);
            blob = new Blob([uint8Array], { type: 'image/jpeg' }); // 假设是JPEG
          } else {
            console.log('内容不是二进制数据，使用签名URL方法');
            throw new Error('需要使用签名URL方法获取图片');
          }
          break;

        default:
          // 其他类型，使用签名URL方法
          console.log(`不支持的ContentType: ${contentType}，使用签名URL方法`);
          throw new Error('需要使用签名URL方法获取图片');
      }

      // 使用FileReader将Blob转换为base64
      const reader = new FileReader();
      const base64 = await new Promise((resolve, reject) => {
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });

      console.log(`图片转换为base64成功: ${path}`);

      // 缓存base64编码
      await storageService.localCache.setItem(cacheKey, base64);
      console.log(`图片base64已缓存: ${path}`);

      return base64;

      } catch (obsError) {
        // 如果使用OBS客户端获取失败，尝试使用签名URL方法
        console.error('使用OBS客户端获取图片失败:', obsError);
        console.log('尝试使用签名URL方法获取图片...');

        // 使用签名URL获取图片
        const signedUrl = await this.getSignedUrl(fullPath);
        console.log(`获取图片签名URL: ${signedUrl}`);

        // 使用fetch获取图片数据
        const response = await fetch(signedUrl);
        if (!response.ok) {
          throw new Error(`获取图片失败，状态码: ${response.status}`);
        }

        // 将响应转换为blob
        const blob = await response.blob();
        console.log(`获取图片blob成功，大小: ${blob.size} 字节`);

        // 将blob转换为base64
        const reader = new FileReader();
        base64 = await new Promise((resolve, reject) => {
          reader.onload = () => resolve(reader.result);
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });

        console.log(`图片转换为base64成功: ${path}`);

        // 缓存base64编码
        await storageService.localCache.setItem(cacheKey, base64);
        console.log(`图片base64已缓存: ${path}`);

        return base64;
      }
    } catch (error) {
      console.error('获取图片base64失败:', error);
      return null;
    }
  }

  /**
   * 为记忆加载缩略图
   * @param {Object} memory - 记忆对象
   * @returns {Promise<Object>} 带有缩略图URL的记忆对象
   * @private
   */
  async _loadMemoryThumbnails(memory) {
    try {
      // 如果记忆没有图片和视频，直接返回
      if ((!memory.has_images && !memory.has_videos) || !memory.id) {
        return memory;
      }

      // 获取完整记忆详情
      const fullMemory = await this.getMemory(memory.id);
      if (!fullMemory) {
        return memory;
      }

      // 创建记忆的副本，不修改原始对象
      const memoryWithThumbnails = { ...memory };

      // 处理图片
      if (fullMemory.images && fullMemory.images.length > 0) {
        // 加载第一张图片的缩略图作为记忆缩略图
        const firstImage = fullMemory.images[0];
        if (firstImage.id) {
          try {
            // 使用getImageUrl获取URL
            console.log('正在加载记忆缩略图...: ', firstImage.id);
            const url = await this.getImageUrl(memory.id, firstImage.id, 'thumbnail');
            console.log('正在加载记忆缩略图...URL: ', url);
            memoryWithThumbnails.thumbnail_url = url;
          } catch (error) {
            console.error('加载记忆缩略图失败:', error);
          }
        }

        // 加载最多9张图片的缩略图
        const imagesToLoad = fullMemory.images.slice(0, 9); // 最多加载9张
        memoryWithThumbnails.images = await Promise.all(imagesToLoad.map(async (image) => {
          if (image.id) {
            try {
              // 如果是第一张图片，已经加载过，直接使用
              if (image.id === firstImage.id && memoryWithThumbnails.thumbnail_url) {
                return { ...image, thumbnail_url: memoryWithThumbnails.thumbnail_url };
              }

              // 否则加载新的缩略图
              console.log('正在加载图片缩略图...: ', image.id);
              const url = await this.getImageUrl(memory.id, image.id, 'thumbnail');
              return { ...image, thumbnail_url: url };
            } catch (error) {
              console.error('加载图片缩略图失败:', error);
              return image;
            }
          }
          return image;
        }));
      }

      // 处理视频
      if (fullMemory.videos && fullMemory.videos.length > 0) {
        // 只在没有图片缩略图的情况下加载视频缩略图作为记忆缩略图
        if (!memoryWithThumbnails.thumbnail_url) {
          const firstVideo = fullMemory.videos[0];
          if (firstVideo.id) {
            try {
              // 使用getVideoUrl获取URL
              console.log('正在加载记忆视频缩略图...: ', firstVideo.id);
              const url = await this.getVideoUrl(memory.id, firstVideo.id, 'thumbnail');
              console.log('正在加载记忆视频缩略图...URL: ', url);
              memoryWithThumbnails.thumbnail_url = url;
            } catch (error) {
              console.error('加载记忆视频缩略图失败:', error);
            }
          }
        }

        // 加载最多9个视频缩略图
        const videosToLoad = fullMemory.videos.slice(0, 9); // 最多加载9个
        memoryWithThumbnails.videos = await Promise.all(videosToLoad.map(async (video) => {
          if (video.id) {
            try {
              // 始终为每个视频加载其自己的缩略图，而不使用记忆缩略图
              // 这样可以确保即使同时有图片和视频，每个视频也使用自己的缩略图
              console.log('正在加载视频缩略图...: ', video.id);
              const url = await this.getVideoUrl(memory.id, video.id, 'thumbnail');
              console.log(`视频 ${video.id} 的缩略图URL: ${url}`);
              return { ...video, thumbnail_url: url };
            } catch (error) {
              console.error('加载视频缩略图失败:', error);
              return video;
            }
          }
          return video;
        }));
      }

      return memoryWithThumbnails;
    } catch (error) {
      console.error('加载记忆缩略图失败:', error);
      return memory; // 出错时返回原始记忆
    }
  }

  /**
   * 获取记忆列表
   * @param {Object} filter - 过滤条件
   * @param {boolean} loadThumbnails - 是否加载缩略图
   * @param {number} page - 页码，从1开始
   * @param {number} pageSize - 每页数量
   * @returns {Promise<{memories: Array, total: number, page: number, pageSize: number}>} 记忆列表和分页信息
   */
  async getMemoryList(filter = {}, loadThumbnails = true, page = 1, pageSize = 20) {
    try {
      console.log(`获取记忆列表: 过滤条件=${JSON.stringify(filter)}, 页码=${page}, 每页=${pageSize}`);

      // 获取元数据
      const metadata = await this.getUserMetadata();
      console.log('在getMemoryList中获取到的元数据:', metadata);

      // 检查元数据是否有效
      if (!metadata || !metadata.memory_index) {
        console.warn('元数据无效或没有memory_index');
        return { memories: [], total: 0, page, pageSize };
      }

      let memoryIds = [];
      let totalCount = 0;

      // 根据过滤条件获取记忆ID
      if (filter.category && metadata.memory_index.by_category) {
        const categoryIndex = metadata.memory_index.by_category[filter.category];
        if (categoryIndex) {
          if (typeof categoryIndex === 'object' && categoryIndex.file) {
            // 新结构，从分类索引文件中获取
            try {
              const categoryIndexData = await this.getObject(`users/${this.userId}/${categoryIndex.file}`);
              memoryIds = categoryIndexData.memories || [];
              totalCount = categoryIndex.count || memoryIds.length;
            } catch (error) {
              console.error(`获取分类索引失败: ${error.message}`);
              // 如果获取失败，使用空数组
              memoryIds = [];
              totalCount = 0;
            }
          } else if (Array.isArray(categoryIndex)) {
            // 旧结构，直接使用元数据中的数组
            memoryIds = categoryIndex;
            totalCount = memoryIds.length;
          }
        }
      } else if (filter.tag && metadata.memory_index.by_tag) {
        const tagIndex = metadata.memory_index.by_tag[filter.tag];
        if (tagIndex) {
          if (typeof tagIndex === 'object' && tagIndex.file) {
            // 新结构，从标签索引文件中获取
            try {
              const tagIndexData = await this.getObject(`users/${this.userId}/${tagIndex.file}`);
              memoryIds = tagIndexData.memories || [];
              totalCount = tagIndex.count || memoryIds.length;
            } catch (error) {
              console.error(`获取标签索引失败: ${error.message}`);
              // 如果获取失败，使用空数组
              memoryIds = [];
              totalCount = 0;
            }
          } else if (Array.isArray(tagIndex)) {
            // 旧结构，直接使用元数据中的数组
            memoryIds = tagIndex;
            totalCount = memoryIds.length;
          }
        }
      } else if (filter.favorite && metadata.memory_index.favorites) {
        memoryIds = metadata.memory_index.favorites || [];
        totalCount = memoryIds.length;
      } else {
        // 没有特定过滤条件，从所有记忆块中获取所有记忆
        console.log('没有特定过滤条件，从所有记忆块中获取所有记忆');

        if (metadata.memory_chunks && metadata.memory_chunks.length > 0) {
          // 使用分块存储结构，从所有块中获取记忆ID
          const allMemoryIds = [];

          for (const chunkInfo of metadata.memory_chunks) {
            try {
              const chunk = await this._getMemoryChunk(chunkInfo.id);
              if (chunk && chunk.memories) {
                allMemoryIds.push(...Object.keys(chunk.memories));
              }
            } catch (error) {
              console.error(`获取记忆块 ${chunkInfo.id} 失败: ${error.message}`);
              // 继续处理其他块
            }
          }

          // 按创建时间排序（最新的在前）
          memoryIds = allMemoryIds;
          totalCount = allMemoryIds.length;

          console.log(`从所有记忆块中获取到 ${allMemoryIds.length} 条记忆`);
        } else if (metadata.memory_index.recent) {
          // 如果没有分块结构，使用recent索引作为备选
          console.log('没有分块结构，使用recent索引');
          memoryIds = metadata.memory_index.recent || [];
          totalCount = metadata.memory_count || memoryIds.length;
        }
      }

      console.log(`获取到的记忆ID列表: ${memoryIds.length} 条`);

      // 如果没有记忆ID，尝试从本地存储获取
      if (!memoryIds || memoryIds.length === 0) {
        console.log('没有记忆ID，尝试从本地存储获取');
        try {
          const localMemories = await storageService.getMemories();
          if (localMemories && localMemories.length > 0) {
            console.log('从本地存储获取到记忆:', localMemories.length);

            // 应用分页
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pagedMemories = localMemories.slice(startIndex, endIndex);

            return {
              memories: pagedMemories,
              total: localMemories.length,
              page,
              pageSize
            };
          }
        } catch (localError) {
          console.error('从本地存储获取记忆失败:', localError);
        }
        return { memories: [], total: 0, page, pageSize };
      }

      // 应用分页
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pagedMemoryIds = memoryIds.slice(startIndex, endIndex);

      console.log(`分页后的记忆ID: ${pagedMemoryIds.length} 条, 起始索引=${startIndex}, 结束索引=${endIndex}`);

      // 从块中获取记忆元数据
      const memories = [];

      for (const memoryId of pagedMemoryIds) {
        try {
          // 先检查记忆ID到块的映射
          const chunkId = metadata.memory_id_to_chunk[memoryId];

          if (chunkId) {
            // 从块中获取记忆元数据
            const chunk = await this._getMemoryChunk(chunkId);

            if (chunk.memories && chunk.memories[memoryId]) {
              memories.push(chunk.memories[memoryId]);
              continue;
            }
          }

          // 如果从块中找不到，尝试从旧结构中获取
          if (metadata.memory_metadata && metadata.memory_metadata[memoryId]) {
            memories.push(metadata.memory_metadata[memoryId]);
          }
        } catch (error) {
          console.error(`获取记忆 ${memoryId} 元数据失败:`, error);
          // 继续处理下一个记忆
        }
      }

      console.log(`成功获取到 ${memories.length} 条记忆元数据`);

      // 如果需要加载缩略图
      if (loadThumbnails && memories.length > 0) {
        console.log('开始加载记忆缩略图...');
        // 并行加载所有记忆的缩略图，最多同时5个
        const batchSize = 5;
        const result = [];

        for (let i = 0; i < memories.length; i += batchSize) {
          const batch = memories.slice(i, i + batchSize);
          const batchPromises = batch.map(memory => this._loadMemoryThumbnails(memory));
          const batchResults = await Promise.all(batchPromises);
          result.push(...batchResults);

          // 每处理一批打印进度
          console.log(`已加载 ${i + batchResults.length} / ${memories.length} 个记忆缩略图`);
        }

        console.log('完成记忆缩略图加载');

        return {
          memories: result,
          total: totalCount,
          page,
          pageSize
        };
      }

      return {
        memories,
        total: totalCount,
        page,
        pageSize
      };
    } catch (error) {
      console.error('获取记忆列表失败:', error);
      // 如果出错，尝试从本地存储获取
      try {
        const localMemories = await storageService.getMemories();
        if (localMemories && localMemories.length > 0) {
          console.log('从本地存储获取到记忆:', localMemories.length);

          // 应用分页
          const startIndex = (page - 1) * pageSize;
          const endIndex = startIndex + pageSize;
          const pagedMemories = localMemories.slice(startIndex, endIndex);

          return {
            memories: pagedMemories,
            total: localMemories.length,
            page,
            pageSize
          };
        }
      } catch (localError) {
        console.error('从本地存储获取记忆失败:', localError);
      }
      return { memories: [], total: 0, page, pageSize };
    }
  }

  /**
   * 获取记忆详情
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<Object>} 记忆详情
   */
  async getMemory(memoryId) {
    try {
      console.log(`开始获取记忆 ${memoryId} 详情...`);

      if (!this.isInitialized) {
        console.log('存储服务未初始化，正在初始化...');
        await this.initialize();
      }

      if (!this.isInitialized) {
        throw new Error('OSS存储服务初始化失败');
      }

      // 先尝试从本地缓存获取
      console.log('尝试从本地缓存获取...');
      const cachedMemory = await this._getMemoryFromCache(memoryId);
      if (cachedMemory) {
        console.log('从缓存中获取到记忆');
        return cachedMemory;
      }

      // 获取元数据
      console.log('正在获取元数据...');
      const metadata = await this.getUserMetadata();

      // 检查元数据是否有效
      if (!metadata) {
        throw new Error('元数据无效');
      }

      // 从元数据中获取记忆所在的块ID
      const chunkId = metadata.memory_id_to_chunk[memoryId];
      if (!chunkId) {
        // 如果在新的分块结构中找不到，尝试从旧的memory_metadata中获取
        if (metadata.memory_metadata && metadata.memory_metadata[memoryId]) {
          const memoryMeta = metadata.memory_metadata[memoryId];
          console.log(`从旧结构中找到记忆 ${memoryId} 元数据:`, memoryMeta);

          if (!memoryMeta.path) {
            throw new Error(`记忆路径不存在: ${memoryId}`);
          }

          // 从OSS获取记忆详情
          console.log(`从 OSS 获取记忆: users/${this.userId}${memoryMeta.path}`);
          const memory = await this.getObject(`users/${this.userId}${memoryMeta.path}`);
          console.log('从 OSS 获取到记忆:', memory);

          // 保存到本地缓存
          await this._saveMemoryToCache(memoryId, memory);
          console.log('记忆已保存到缓存');

          return memory;
        }

        throw new Error(`找不到记忆所在的块: ${memoryId}`);
      }

      // 获取记忆块
      console.log(`从块 ${chunkId} 中获取记忆...`);
      const chunk = await this._getMemoryChunk(chunkId);

      // 检查记忆是否存在于块中
      if (!chunk.memories || !chunk.memories[memoryId]) {
        throw new Error(`记忆不存在于块中: ${memoryId}`);
      }

      // 获取记忆元数据
      const memoryMeta = chunk.memories[memoryId];
      console.log(`从块中获取到记忆元数据:`, memoryMeta);

      if (!memoryMeta.path) {
        throw new Error(`记忆路径不存在: ${memoryId}`);
      }

      // 从OSS获取记忆详情
      console.log(`从 OSS 获取记忆: users/${this.userId}${memoryMeta.path}`);
      const memory = await this.getObject(`users/${this.userId}${memoryMeta.path}`);
      console.log('从 OSS 获取到记忆:', memory);

      // 保存到本地缓存
      await this._saveMemoryToCache(memoryId, memory);
      console.log('记忆已保存到缓存');

      return memory;
    } catch (error) {
      console.error(`获取记忆 ${memoryId} 详情失败:`, error);
      throw error;
    }
  }

  /**
   * 添加新记忆
   * @param {Object} memoryData - 记忆数据
   * @param {Array} images - 图片文件数组
   * @param {Array} videos - 视频文件数组
   * @returns {Promise<string>} 记忆ID
   */
  async addMemory(memoryData, images = [], videos = []) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    // 生成唯一ID
    const memoryId = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 使用历史时间或当前时间
    const now = memoryData.historicalDate ? new Date(memoryData.historicalDate) : new Date();
    console.log('使用的时间:', now, memoryData.historicalDate ? '(历史时间)' : '(当前时间)');

    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');

    // 创建记忆对象
    const memory = {
      id: memoryId,
      title: memoryData.title,
      content: memoryData.content,
      created_at: now.toISOString(),
      updated_at: new Date().toISOString(), // 更新时间始终使用当前时间
      category: memoryData.category,
      tags: memoryData.tags || [],
      images: [],
      videos: []
    };

    // 处理图片
    if (images && images.length > 0) {
      for (const image of images) {
        const imageData = await this._processAndUploadImage(memoryId, image, now);
        memory.images.push(imageData);
      }
    }

    // 处理视频
    if (videos && videos.length > 0) {
      for (const video of videos) {
        const videoData = await this._processAndUploadVideo(memoryId, video, now);
        memory.videos.push(videoData);
      }
    }

    // 保存记忆JSON文件
    const memoryPath = `/memories/${year}/${month}/${memoryId}.json`;
    await this.putObject(`users/${this.userId}${memoryPath}`, JSON.stringify(memory));

    // 创建记忆元数据
    const memoryMeta = {
      id: memoryId,
      title: memory.title,
      content: memory.content, // 存储全部内容
      created_at: memory.created_at,
      category: memory.category,
      tags: memory.tags,
      has_images: memory.images.length > 0,
      has_videos: memory.videos.length > 0,
      path: memoryPath
    };

    // 获取元数据
    const metadata = await this.getUserMetadata();

    // 获取或创建合适的记忆块，使用记忆的创建时间
    const createdAt = new Date(memory.created_at);
    const { chunkId, chunk } = await this._getOrCreateChunkForMemory(createdAt);

    // 将记忆添加到块
    await this._addMemoryToChunk(chunkId, memoryId, memoryMeta);

    // 更新元数据中的块信息
    const chunkIndex = metadata.memory_chunks.findIndex(c => c.id === chunkId);
    if (chunkIndex !== -1) {
      metadata.memory_chunks[chunkIndex].count++;
    }

    // 更新记忆ID到块的映射
    metadata.memory_id_to_chunk[memoryId] = chunkId;

    // 更新记忆计数
    metadata.memory_count++;

    // 更新最近记忆索引
    // 如果是历史时间，我们需要根据创建时间插入到正确的位置
    const createdAtTime = new Date(memory.created_at).getTime();

    // 找到正确的插入位置
    let insertIndex = 0;
    try {
      for (let i = 0; i < metadata.memory_index.recent.length; i++) {
        const existingMemoryId = metadata.memory_index.recent[i];
        let existingMemory = null;

        try {
          // 先检查内存块中是否有该记忆
          if (metadata.memory_id_to_chunk[existingMemoryId]) {
            existingMemory = await this._getMemoryMetaFromChunk(existingMemoryId);
          }
          // 如果内存块中没有，则检查旧结构
          else if (metadata.memory_metadata && metadata.memory_metadata[existingMemoryId]) {
            existingMemory = metadata.memory_metadata[existingMemoryId];
          }

          // 如果找到了记忆并且有创建时间，则比较时间
          if (existingMemory && existingMemory.created_at &&
              new Date(existingMemory.created_at).getTime() <= createdAtTime) {
            insertIndex = i;
            break;
          }
        } catch (memoryError) {
          console.error(`获取记忆 ${existingMemoryId} 元数据失败:`, memoryError);
          // 继续处理下一个记忆
          continue;
        }
      }
    } catch (error) {
      console.error('查找插入位置时出错:', error);
      // 出错时使用默认插入位置 0
    }

    // 在正确的位置插入记忆ID
    metadata.memory_index.recent.splice(insertIndex, 0, memoryId);

    // 保持最近记忆列表不超过100条
    if (metadata.memory_index.recent.length > 100) {
      metadata.memory_index.recent.pop();
    }

    // 更新分类索引
    if (!metadata.memory_index.by_category[memory.category]) {
      metadata.memory_index.by_category[memory.category] = {
        count: 0,
        file: `indexes/categories/category_${memory.category}.json`
      };
    }
    metadata.memory_index.by_category[memory.category].count++;

    // 获取分类索引文件
    try {
      // 确保分类存在且有效
      if (!memory.category) {
        console.warn('记忆没有分类，跳过分类索引更新');
        throw new Error('记忆没有分类');
      }

      if (!metadata.memory_index.by_category[memory.category] ||
          !metadata.memory_index.by_category[memory.category].file) {
        console.warn(`分类 ${memory.category} 索引不存在或无效，重新创建`);
        metadata.memory_index.by_category[memory.category] = {
          count: 0,
          file: `indexes/categories/category_${memory.category}.json`
        };
      }

      const categoryIndexPath = metadata.memory_index.by_category[memory.category].file;
      let categoryIndexData;

      try {
        // 尝试获取现有索引文件
        categoryIndexData = await this.getObject(`users/${this.userId}/${categoryIndexPath}`);

        // 验证获取到的数据是否有效
        if (!categoryIndexData || !Array.isArray(categoryIndexData.memories)) {
          throw new Error('分类索引数据无效');
        }
      } catch (error) {
        console.log(`创建新的分类索引数据: ${error.message}`);
        // 如果文件不存在或数据无效，创建新的索引数据
        categoryIndexData = {
          category_id: memory.category,
          count: 0,
          last_updated: new Date().toISOString(),
          memories: []
        };
      }

      // 根据创建时间插入记忆ID
      const memoryCreatedAt = new Date(memory.created_at).getTime();
      let insertIndex = categoryIndexData.memories.length; // 默认插入到末尾

      try {
        // 找到正确的插入位置
        for (let i = 0; i < categoryIndexData.memories.length; i++) {
          const existingMemoryId = categoryIndexData.memories[i];
          let existingMemory = null;

          try {
            // 先检查内存块中是否有该记忆
            if (metadata.memory_id_to_chunk && metadata.memory_id_to_chunk[existingMemoryId]) {
              existingMemory = await this._getMemoryMetaFromChunk(existingMemoryId);
            }
            // 如果内存块中没有，则检查旧结构
            else if (metadata.memory_metadata && metadata.memory_metadata[existingMemoryId]) {
              existingMemory = metadata.memory_metadata[existingMemoryId];
            }

            // 如果找到了记忆并且有创建时间，则比较时间
            if (existingMemory && existingMemory.created_at &&
                new Date(existingMemory.created_at).getTime() <= memoryCreatedAt) {
              insertIndex = i;
              break;
            }
          } catch (memoryError) {
            console.error(`获取分类索引中的记忆 ${existingMemoryId} 元数据失败:`, memoryError);
            // 继续处理下一个记忆
            continue;
          }
        }
      } catch (error) {
        console.error('查找分类索引插入位置时出错:', error);
        // 出错时使用默认插入位置（末尾）
      }

      // 在正确的位置插入记忆ID
      categoryIndexData.memories.splice(insertIndex, 0, memoryId);
      categoryIndexData.count = categoryIndexData.memories.length;
      categoryIndexData.last_updated = new Date().toISOString();

      // 保存更新后的索引文件
      await this.putObject(`users/${this.userId}/${categoryIndexPath}`, JSON.stringify(categoryIndexData));
    } catch (error) {
      console.error(`更新分类索引文件失败: ${error.message}`);
      // 继续执行，不阻止记忆的添加
    }

    // 更新标签索引
    for (const tag of memory.tags) {
      if (!metadata.memory_index.by_tag[tag]) {
        metadata.memory_index.by_tag[tag] = {
          count: 0,
          file: `indexes/tags/tag_${tag.replace(/[^a-zA-Z0-9]/g, '_')}.json`
        };
      }
      metadata.memory_index.by_tag[tag].count++;

      // 获取标签索引文件
      try {
        // 确保标签存在且有效
        if (!tag) {
          console.warn('标签为空，跳过标签索引更新');
          continue;
        }

        if (!metadata.memory_index.by_tag[tag] ||
            !metadata.memory_index.by_tag[tag].file) {
          console.warn(`标签 ${tag} 索引不存在或无效，重新创建`);
          metadata.memory_index.by_tag[tag] = {
            count: 0,
            file: `indexes/tags/tag_${tag.replace(/[^a-zA-Z0-9]/g, '_')}.json`
          };
        }

        const tagIndexPath = metadata.memory_index.by_tag[tag].file;
        let tagIndexData;

        try {
          // 尝试获取现有索引文件
          tagIndexData = await this.getObject(`users/${this.userId}/${tagIndexPath}`);

          // 验证获取到的数据是否有效
          if (!tagIndexData || !Array.isArray(tagIndexData.memories)) {
            throw new Error('标签索引数据无效');
          }
        } catch (error) {
          console.log(`创建新的标签索引数据: ${error.message}`);
          // 如果文件不存在或数据无效，创建新的索引数据
          tagIndexData = {
            tag: tag,
            count: 0,
            last_updated: new Date().toISOString(),
            memories: []
          };
        }

        // 根据创建时间插入记忆ID
        const memoryCreatedAt = new Date(memory.created_at).getTime();
        let insertIndex = tagIndexData.memories.length; // 默认插入到末尾

        try {
          // 找到正确的插入位置
          for (let i = 0; i < tagIndexData.memories.length; i++) {
            const existingMemoryId = tagIndexData.memories[i];
            let existingMemory = null;

            try {
              // 先检查内存块中是否有该记忆
              if (metadata.memory_id_to_chunk && metadata.memory_id_to_chunk[existingMemoryId]) {
                existingMemory = await this._getMemoryMetaFromChunk(existingMemoryId);
              }
              // 如果内存块中没有，则检查旧结构
              else if (metadata.memory_metadata && metadata.memory_metadata[existingMemoryId]) {
                existingMemory = metadata.memory_metadata[existingMemoryId];
              }

              // 如果找到了记忆并且有创建时间，则比较时间
              if (existingMemory && existingMemory.created_at &&
                  new Date(existingMemory.created_at).getTime() <= memoryCreatedAt) {
                insertIndex = i;
                break;
              }
            } catch (memoryError) {
              console.error(`获取标签索引中的记忆 ${existingMemoryId} 元数据失败:`, memoryError);
              // 继续处理下一个记忆
              continue;
            }
          }
        } catch (error) {
          console.error('查找标签索引插入位置时出错:', error);
          // 出错时使用默认插入位置（末尾）
        }

        // 在正确的位置插入记忆ID
        tagIndexData.memories.splice(insertIndex, 0, memoryId);
        tagIndexData.count = tagIndexData.memories.length;
        tagIndexData.last_updated = new Date().toISOString();

        // 保存更新后的索引文件
        await this.putObject(`users/${this.userId}/${tagIndexPath}`, JSON.stringify(tagIndexData));
      } catch (error) {
        console.error(`更新标签索引文件失败: ${error.message}`);
        // 继续执行，不阻止记忆的添加
      }
    }

    // 保存更新后的元数据
    await this.updateUserMetadata(metadata);

    // 保存到本地缓存
    await this._saveMemoryToCache(memoryId, memory);

    // 将记忆添加到搜索索引
    try {
      await searchIndexService.indexMemory(memory);
      console.log(`记忆 ${memoryId} 已添加到搜索索引`);
    } catch (indexError) {
      console.error(`将记忆添加到搜索索引失败: ${indexError.message}`);
      // 不阻止记忆的添加，继续返回记忆ID
    }

    return memoryId;
  }

  /**
   * 获取图片URL
   * @param {string} memoryId - 记忆ID
   * @param {string} imageId - 图片ID
   * @param {string} size - 图片尺寸，可选值：original, thumbnail
   * @returns {Promise<string>} 图片URL
   */
  async getImageUrl(memoryId, imageId, size = 'thumbnail') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    // 获取记忆详情
    const memory = await this.getMemory(memoryId);

    // 查找图片
    const image = memory.images.find(img => img.id === imageId);
    if (!image) {
      throw new Error(`图片不存在: ${imageId}`);
    }

    // 根据尺寸选择路径
    const path = size === 'thumbnail' ? image.thumbnail_path : image.path;

    // 生成临时URL
    return await this.getSignedUrl(`users/${this.userId}${path}`);
  }

  /**
   * 获取图片的base64编码
   * @param {string} memoryId - 记忆ID
   * @param {string} imageId - 图片ID
   * @param {string} size - 图片尺寸，可选值：original, thumbnail
   * @returns {Promise<string>} base64编码的图片
   */
  async getImageBase64(memoryId, imageId, size = 'thumbnail') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    // 获取记忆详情
    const memory = await this.getMemory(memoryId);

    // 查找图片
    const image = memory.images.find(img => img.id === imageId);
    if (!image) {
      throw new Error(`图片不存在: ${imageId}`);
    }

    // 根据尺寸选择路径
    const path = size === 'thumbnail' ? image.thumbnail_path : image.path;

    console.log(`获取图片base64: ${path}`);

    // 获取base64编码
    return await this._getImageBase64(path);
  }

  /**
   * 获取视频URL
   * @param {string} memoryId - 记忆ID
   * @param {string} videoId - 视频ID
   * @param {string} type - 视频类型，可选值：original, preview, thumbnail
   * @returns {Promise<string>} 视频URL
   */
  async getVideoUrl(memoryId, videoId, type = 'preview') {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log(`获取视频URL: memoryId=${memoryId}, videoId=${videoId}, type=${type}`)
    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    // 获取记忆详情
    const memory = await this.getMemory(memoryId);

    // 查找视频
    const video = memory.videos.find(vid => vid.id === videoId);
    if (!video) {
      throw new Error(`视频不存在: ${videoId}`);
    }

    // 根据类型选择路径
    let path;
    if (type === 'thumbnail') {
      path = video.thumbnail_path;
      console.log(`使用视频缩略图路径: ${path}`);

      // 对于缩略图，直接调用getImageUrl方法，因为缩略图是图片格式
      const fullPath = `users/${this.userId}${path}`;
      console.log(`视频缩略图完整路径: ${fullPath}`);

      // 获取签名URL
      const signedUrl = await this.getSignedUrl(fullPath);
      console.log(`获取到视频缩略图URL: ${signedUrl}`);
      return signedUrl;
    } else if (type === 'preview') {
      path = video.preview_path || video.path; // 如果没有预览路径，使用原始路径
      console.log(`使用视频预览路径: ${path}`);
    } else {
      path = video.path;
      console.log(`使用视频原始路径: ${path}`);
    }

    // 获取完整路径
    const fullPath = `users/${this.userId}${path}`;
    console.log(`视频完整路径: ${fullPath}`);

    // 获取签名URL
    const signedUrl = await this.getSignedUrl(fullPath);
    console.log(`获取到视频URL: ${signedUrl}`);
    return signedUrl;
  }

  /**
   * 获取视频缩略图的base64编码
   * @param {string} memoryId - 记忆ID
   * @param {string} videoId - 视频ID
   * @returns {Promise<string>} base64编码的缩略图
   */
  async getVideoThumbnailBase64(memoryId, videoId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    // 获取记忆详情
    const memory = await this.getMemory(memoryId);

    // 查找视频
    const video = memory.videos.find(vid => vid.id === videoId);
    if (!video) {
      throw new Error(`视频不存在: ${videoId}`);
    }

    // 获取缩略图路径
    const path = video.thumbnail_path;
    if (!path) {
      throw new Error(`视频缩略图不存在: ${videoId}`);
    }

    console.log(`获取视频缩略图base64: ${path}`);

    // 获取base64编码
    return await this._getImageBase64(path);
  }

  /**
   * 处理并上传图片
   * @param {string} memoryId - 记忆ID
   * @param {File} image - 图片文件
   * @param {Date} [createdAt] - 创建时间，默认为当前时间
   * @returns {Promise<Object>} 图片元数据
   */
  async _processAndUploadImage(memoryId, image, createdAt) {
    // 生成唯一ID
    const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const extension = image.name.split('.').pop().toLowerCase();
    const now = createdAt ? createdAt.toISOString() : new Date().toISOString();

    // 创建路径
    const originalPath = `/assets/images/${memoryId}/${imageId}.${extension}`;
    const thumbnailPath = `/assets/images/${memoryId}/thumbnails/${imageId}_thumb.${extension}`;

    // 上传原始图片
    await this.putObject(`users/${this.userId}${originalPath}`, image, image.type);

    // 生成缩略图
    const thumbnail = await this._generateImageThumbnail(image);

    // 上传缩略图
    await this.putObject(`users/${this.userId}${thumbnailPath}`, thumbnail, image.type);

    // 返回图片元数据
    return {
      id: imageId,
      filename: image.name,
      path: originalPath,
      thumbnail_path: thumbnailPath,
      size: image.size,
      width: image.width || 0,
      height: image.height || 0,
      created_at: now
    };
  }

  /**
   * 处理并上传视频
   * @param {string} memoryId - 记忆ID
   * @param {File} video - 视频文件
   * @param {Date} [createdAt] - 创建时间，默认为当前时间
   * @returns {Promise<Object>} 视频元数据
   */
  async _processAndUploadVideo(memoryId, video, createdAt) {
    // 生成唯一ID
    const videoId = `vid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const extension = video.name.split('.').pop().toLowerCase();
    const now = createdAt ? createdAt.toISOString() : new Date().toISOString();

    // 创建路径
    const originalPath = `/assets/videos/${memoryId}/${videoId}.${extension}`;
    const thumbnailPath = `/assets/videos/${memoryId}/thumbnails/${videoId}_thumb.jpg`;

    // 上传原始视频
    console.log(`上传原始视频: ${videoId}, 路径: ${originalPath}`);
    await this.putObject(`users/${this.userId}${originalPath}`, video, video.type);

    // 生成缩略图
    console.log(`生成视频缩略图: ${videoId}`);
    const thumbnail = await this._generateVideoThumbnail(video);
    console.log(`视频缩略图生成成功: ${videoId}, 大小: ${thumbnail.size} 字节`);

    // 上传缩略图
    console.log(`上传视频缩略图: ${videoId}, 路径: ${thumbnailPath}`);
    await this.putObject(`users/${this.userId}${thumbnailPath}`, thumbnail, 'image/jpeg');
    console.log(`视频缩略图上传成功: ${videoId}`);

    // 获取视频元数据
    let duration = 0;
    let width = 0;
    let height = 0;

    try {
      // 尝试获取视频元数据
      const videoEl = document.createElement('video');
      await new Promise((resolve, reject) => {
        videoEl.onloadedmetadata = () => {
          duration = videoEl.duration;
          width = videoEl.videoWidth;
          height = videoEl.videoHeight;
          resolve();
        };
        videoEl.onerror = () => reject(new Error('获取视频元数据失败'));
        videoEl.src = URL.createObjectURL(video);
        videoEl.load();

        // 设置超时
        setTimeout(() => resolve(), 3000); // 3秒超时
      });
    } catch (error) {
      console.error('获取视频元数据失败:', error);
    }

    // 返回视频元数据
    return {
      id: videoId,
      filename: video.name,
      path: originalPath,
      preview_path: originalPath, // 使用原始视频路径作为预览路径
      thumbnail_path: thumbnailPath,
      size: video.size,
      duration: duration,
      width: width,
      height: height,
      created_at: now
    };
  }

  /**
   * 生成图片缩略图
   * @param {File} image - 图片文件
   * @returns {Promise<Blob>} 缩略图Blob
   */
  async _generateImageThumbnail(image) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      img.onload = () => {
        // 计算缩略图尺寸，保持宽高比
        const maxSize = 200;
        let width = img.width;
        let height = img.height;

        if (width > height) {
          if (width > maxSize) {
            height = Math.round(height * maxSize / width);
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = Math.round(width * maxSize / height);
            height = maxSize;
          }
        }

        // 设置canvas尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制缩略图
        ctx.drawImage(img, 0, 0, width, height);

        // 转换为Blob
        canvas.toBlob(blob => {
          resolve(blob);
        }, image.type);
      };

      img.onerror = () => {
        reject(new Error('生成缩略图失败'));
      };

      // 加载图片
      img.src = URL.createObjectURL(image);
    });
  }

  /**
   * 生成视频缩略图
   * @param {File} video - 视频文件
   * @returns {Promise<Blob>} 缩略图Blob
   */
  async _generateVideoThumbnail(video) {
    return new Promise((resolve, reject) => {
      const videoEl = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      let hasResolved = false;
      let timeoutId = null;

      // 设置超时处理
      timeoutId = setTimeout(() => {
        if (!hasResolved) {
          console.warn('生成视频缩略图超时，使用默认缩略图');
          // 创建一个默认的缩略图
          canvas.width = 320;
          canvas.height = 240;
          ctx.fillStyle = '#000000';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
          ctx.fillStyle = '#ffffff';
          ctx.font = '20px Arial';
          ctx.textAlign = 'center';
          ctx.textBaseline = 'middle';
          ctx.fillText('视频', canvas.width / 2, canvas.height / 2);

          canvas.toBlob(blob => {
            hasResolved = true;
            resolve(blob);
          }, 'image/jpeg', 0.8);
        }
      }, 5000); // 5秒超时

      videoEl.onloadedmetadata = () => {
        console.log(`视频元数据加载成功: 时长=${videoEl.duration}秒, 尺寸=${videoEl.videoWidth}x${videoEl.videoHeight}`);
        // 设置视频时间到中间位置
        videoEl.currentTime = Math.min(videoEl.duration / 2, 5); // 最多去5秒处
      };

      videoEl.onseeked = () => {
        if (hasResolved) return; // 如果已经解析，不再处理

        // 计算缩略图尺寸，保持宽高比
        const maxSize = 320;
        let width = videoEl.videoWidth;
        let height = videoEl.videoHeight;

        if (width > height) {
          if (width > maxSize) {
            height = Math.round(height * maxSize / width);
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = Math.round(width * maxSize / height);
            height = maxSize;
          }
        }

        // 设置canvas尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制视频帧
        ctx.drawImage(videoEl, 0, 0, width, height);

        // 添加视频标识
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(width - 40, 5, 35, 20);
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('视频', width - 22, 17);

        // 转换为Blob
        canvas.toBlob(blob => {
          if (hasResolved) return; // 如果已经解析，不再处理
          hasResolved = true;
          clearTimeout(timeoutId);
          console.log(`生成视频缩略图成功: 尺寸=${width}x${height}, 大小=${blob.size}字节`);
          resolve(blob);
        }, 'image/jpeg', 0.8); // 使用0.8的质量来减小文件大小
      };

      videoEl.onerror = (e) => {
        if (hasResolved) return; // 如果已经解析，不再处理
        console.error('加载视频失败:', e);
        // 不立即拒绝，让超时处理来创建默认缩略图
      };

      // 加载视频
      try {
        const videoUrl = URL.createObjectURL(video);
        videoEl.src = videoUrl;
        videoEl.load();
        console.log(`开始加载视频: ${videoUrl}`);
      } catch (error) {
        console.error('创建视频URL失败:', error);
        // 不立即拒绝，让超时处理来创建默认缩略图
      }
    });
  }

  /**
   * 从本地缓存获取记忆
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<Object>} 记忆对象，如果不存在则返回null
   */
  async _getMemoryFromCache(memoryId) {
    try {
      const cacheKey = `memory_${memoryId}`;
      return await storageService.getCacheItem(cacheKey, 'memory');
    } catch (error) {
      console.error('从缓存获取记忆失败:', error);
      return null;
    }
  }

  /**
   * 保存记忆到本地缓存
   * @param {string} memoryId - 记忆ID
   * @param {Object} memory - 记忆对象
   * @returns {Promise<void>}
   */
  async _saveMemoryToCache(memoryId, memory) {
    try {
      const cacheKey = `memory_${memoryId}`;
      await storageService.setCacheItem(cacheKey, memory, 'memory');
    } catch (error) {
      console.error('保存记忆到缓存失败:', error);
    }
  }

  /**
   * 从本地缓存删除记忆
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<void>}
   */
  async _removeMemoryFromCache(memoryId) {
    try {
      const cacheKey = `memory_${memoryId}`;
      await storageService.removeCacheItem(cacheKey, 'memory');
    } catch (error) {
      console.error('从缓存删除记忆失败:', error);
    }
  }

  /**
   * 创建新的记忆块
   * @param {string} chunkId - 块ID
   * @param {Date} startDate - 块开始日期
   * @param {Date} endDate - 块结束日期
   * @returns {Promise<Object>} 新创建的块
   * @private
   */
  async _createMemoryChunk(chunkId, startDate, endDate) {
    try {
      console.log(`创建新的记忆块: ${chunkId}`);

      // 创建块对象
      const chunk = {
        chunk_id: chunkId,
        memory_count: 0,
        date_range: [startDate.toISOString(), endDate.toISOString()],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        memories: {}
      };

      // 保存块到OSS
      const chunkPath = `users/${this.userId}/chunks/chunk_${chunkId}.json`;
      await this.putObject(chunkPath, JSON.stringify(chunk));

      return chunk;
    } catch (error) {
      console.error(`创建记忆块失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取记忆块
   * @param {string} chunkId - 块ID
   * @returns {Promise<Object>} 记忆块
   * @private
   */
  async _getMemoryChunk(chunkId) {
    try {
      console.log(`获取记忆块: ${chunkId}`);

      // 先从缓存中获取
      const cacheKey = `chunk_${chunkId}`;
      const cachedChunk = await storageService.getCacheItem(cacheKey, 'chunk');
      if (cachedChunk) {
        console.log(`从缓存中获取到记忆块: ${chunkId}`);
        return cachedChunk;
      }

      // 从 OSS 获取块
      const chunkPath = `users/${this.userId}/chunks/chunk_${chunkId}.json`;
      const chunk = await this.getObject(chunkPath);

      // 保存到缓存
      await storageService.setCacheItem(cacheKey, chunk, 'chunk');

      return chunk;
    } catch (error) {
      console.error(`获取记忆块失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新记忆块
   * @param {string} chunkId - 块ID
   * @param {Object} chunk - 记忆块对象
   * @returns {Promise<void>}
   * @private
   */
  async _updateMemoryChunk(chunkId, chunk) {
    try {
      console.log(`更新记忆块: ${chunkId}`);

      // 更新时间戳
      chunk.updated_at = new Date().toISOString();

      // 保存块到OSS
      const chunkPath = `users/${this.userId}/chunks/chunk_${chunkId}.json`;
      await this.putObject(chunkPath, JSON.stringify(chunk));

      // 更新缓存
      const cacheKey = `chunk_${chunkId}`;
      await storageService.setCacheItem(cacheKey, chunk, 'chunk');

      console.log(`记忆块更新成功: ${chunkId}`);
    } catch (error) {
      console.error(`更新记忆块失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 将记忆添加到块
   * @param {string} chunkId - 块ID
   * @param {string} memoryId - 记忆ID
   * @param {Object} memoryMeta - 记忆元数据
   * @returns {Promise<void>}
   * @private
   */
  async _addMemoryToChunk(chunkId, memoryId, memoryMeta) {
    try {
      console.log(`将记忆添加到块: ${chunkId}, 记忆ID: ${memoryId}`);

      // 获取块
      const chunk = await this._getMemoryChunk(chunkId);

      // 添加记忆
      chunk.memories[memoryId] = memoryMeta;
      chunk.memory_count = Object.keys(chunk.memories).length;

      // 更新块
      await this._updateMemoryChunk(chunkId, chunk);

      console.log(`记忆添加到块成功: ${chunkId}, 记忆ID: ${memoryId}`);
    } catch (error) {
      console.error(`将记忆添加到块失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 从块中获取记忆元数据
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<Object>} 记忆元数据
   * @private
   */
  async _getMemoryMetaFromChunk(memoryId) {
    try {
      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 获取记忆所在的块ID
      const chunkId = metadata.memory_id_to_chunk[memoryId];
      if (!chunkId) {
        return null;
      }

      // 获取块
      const chunk = await this._getMemoryChunk(chunkId);

      // 返回记忆元数据
      return chunk.memories && chunk.memories[memoryId] ? chunk.memories[memoryId] : null;
    } catch (error) {
      console.error(`从块中获取记忆元数据失败: ${error.message}`);
      return null;
    }
  }

  /**
   * 从块中删除记忆
   * @param {string} chunkId - 块ID
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<void>}
   * @private
   */
  async _removeMemoryFromChunk(chunkId, memoryId) {
    try {
      console.log(`从块中删除记忆: ${chunkId}, 记忆ID: ${memoryId}`);

      // 获取块
      const chunk = await this._getMemoryChunk(chunkId);

      // 删除记忆
      if (chunk.memories[memoryId]) {
        delete chunk.memories[memoryId];
        chunk.memory_count = Object.keys(chunk.memories).length;

        // 更新块
        await this._updateMemoryChunk(chunkId, chunk);

        console.log(`从块中删除记忆成功: ${chunkId}, 记忆ID: ${memoryId}`);
      } else {
        console.log(`记忆不存在于块中: ${chunkId}, 记忆ID: ${memoryId}`);
      }
    } catch (error) {
      console.error(`从块中删除记忆失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取或创建合适的记忆块
   * @param {Date} memoryDate - 记忆日期
   * @returns {Promise<{chunkId: string, chunk: Object}>} 块ID和块对象
   * @private
   */
  async _getOrCreateChunkForMemory(memoryDate) {
    try {
      console.log(`获取或创建记忆块, 记忆日期: ${memoryDate.toISOString()}`);

      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 如果没有块，创建第一个块
      if (!metadata.memory_chunks || metadata.memory_chunks.length === 0) {
        console.log('没有现有块，创建第一个块');

        // 创建第一个块，范围从当前日期开始
        const startDate = new Date(memoryDate);
        startDate.setHours(0, 0, 0, 0);

        // 结束日期设置为开始日期后的一年
        const endDate = new Date(startDate);
        endDate.setFullYear(endDate.getFullYear() + 1);
        endDate.setMilliseconds(-1); // 减去1毫秒，使其为23:59:59.999

        const chunkId = `chunk_${Date.now()}`;
        const chunk = await this._createMemoryChunk(chunkId, startDate, endDate);

        // 更新元数据
        metadata.memory_chunks.push({
          id: chunkId,
          file: `chunks/chunk_${chunkId}.json`,
          count: 0,
          date_range: [startDate.toISOString(), endDate.toISOString()]
        });
        metadata.chunk_count = metadata.memory_chunks.length;

        await this.updateUserMetadata(metadata);

        return { chunkId, chunk };
      }

      // 查找合适的块
      for (const chunkInfo of metadata.memory_chunks) {
        const dateRange = chunkInfo.date_range;
        const startDate = new Date(dateRange[0]);
        const endDate = new Date(dateRange[1]);

        // 检查记忆日期是否在块的日期范围内
        if (memoryDate >= startDate && memoryDate <= endDate) {
          console.log(`找到合适的块: ${chunkInfo.id}`);

          // 检查块是否已满
          if (chunkInfo.count >= 1000) {
            console.log(`块已满，需要创建新块: ${chunkInfo.id}`);

            // 创建新块，使用相同的日期范围
            const newChunkId = `chunk_${Date.now()}`;
            const newChunk = await this._createMemoryChunk(newChunkId, startDate, endDate);

            // 更新元数据
            metadata.memory_chunks.push({
              id: newChunkId,
              file: `chunks/chunk_${newChunkId}.json`,
              count: 0,
              date_range: [startDate.toISOString(), endDate.toISOString()]
            });
            metadata.chunk_count = metadata.memory_chunks.length;

            await this.updateUserMetadata(metadata);

            return { chunkId: newChunkId, chunk: newChunk };
          }

          // 获取块
          const chunk = await this._getMemoryChunk(chunkInfo.id);
          return { chunkId: chunkInfo.id, chunk };
        }
      }

      // 如果没有找到合适的块，创建新块
      console.log('没有找到合适的块，创建新块');

      // 创建新块，范围从当前日期开始
      const startDate = new Date(memoryDate);
      startDate.setHours(0, 0, 0, 0);

      // 结束日期设置为开始日期后的一年
      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 1);
      endDate.setMilliseconds(-1); // 减去1毫秒，使其为23:59:59.999

      const chunkId = `chunk_${Date.now()}`;
      const chunk = await this._createMemoryChunk(chunkId, startDate, endDate);

      // 更新元数据
      metadata.memory_chunks.push({
        id: chunkId,
        file: `chunks/chunk_${chunkId}.json`,
        count: 0,
        date_range: [startDate.toISOString(), endDate.toISOString()]
      });
      metadata.chunk_count = metadata.memory_chunks.length;

      await this.updateUserMetadata(metadata);

      return { chunkId, chunk };
    } catch (error) {
      console.error(`获取或创建记忆块失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除记忆
   * @param {string} memoryId - 记忆ID
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteMemory(memoryId) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.isInitialized) {
        throw new Error('OSS存储服务未初始化');
      }

      console.log(`开始删除记忆: ${memoryId}`);

      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 获取记忆所在的块ID
      let chunkId = metadata.memory_id_to_chunk[memoryId];
      let memoryMeta;

      // 如果在新的分块结构中找不到，尝试从旧的memory_metadata中获取
      if (!chunkId) {
        if (metadata.memory_metadata && metadata.memory_metadata[memoryId]) {
          memoryMeta = metadata.memory_metadata[memoryId];
          console.log(`从旧结构中找到记忆 ${memoryId} 元数据:`, memoryMeta);
        } else {
          console.warn(`记忆不存在: ${memoryId}`);
          return false;
        }
      } else {
        // 从块中获取记忆元数据
        try {
          const chunk = await this._getMemoryChunk(chunkId);
          if (!chunk.memories || !chunk.memories[memoryId]) {
            console.warn(`记忆不存在于块中: ${memoryId}`);
            return false;
          }
          memoryMeta = chunk.memories[memoryId];
        } catch (error) {
          console.error(`获取记忆块失败: ${error.message}`);
          // 如果无法获取块，尝试从旧结构中获取
          if (metadata.memory_metadata && metadata.memory_metadata[memoryId]) {
            memoryMeta = metadata.memory_metadata[memoryId];
            console.log(`从旧结构中找到记忆 ${memoryId} 元数据:`, memoryMeta);
          } else {
            console.warn(`记忆不存在: ${memoryId}`);
            return false;
          }
        }
      }

      // 获取完整记忆详情
      let memory;
      try {
        memory = await this.getMemory(memoryId);
      } catch (error) {
        console.warn(`无法获取记忆详情: ${error.message}`);
        // 即使无法获取记忆详情，仍然继续删除元数据
      }

      // 如果成功获取到记忆详情，删除相关资源
      if (memory) {
        // 1. 删除图片
        if (memory.images && memory.images.length > 0) {
          for (const image of memory.images) {
            try {
              // 删除原始图片
              if (image.path) {
                await this.deleteObject(`users/${this.userId}${image.path}`);
              }

              // 删除缩略图
              if (image.thumbnail_path) {
                await this.deleteObject(`users/${this.userId}${image.thumbnail_path}`);
              }
            } catch (error) {
              console.error(`删除图片失败: ${error.message}`);
              // 继续删除其他资源
            }
          }
        }

        // 2. 删除视频
        if (memory.videos && memory.videos.length > 0) {
          for (const video of memory.videos) {
            try {
              // 删除原始视频
              if (video.path) {
                await this.deleteObject(`users/${this.userId}${video.path}`);
              }

              // 删除缩略图
              if (video.thumbnail_path) {
                await this.deleteObject(`users/${this.userId}${video.thumbnail_path}`);
              }
            } catch (error) {
              console.error(`删除视频失败: ${error.message}`);
              // 继续删除其他资源
            }
          }
        }
      }

      // 3. 删除记忆JSON文件
      if (memoryMeta.path) {
        try {
          await this.deleteObject(`users/${this.userId}${memoryMeta.path}`);
        } catch (error) {
          console.error(`删除记忆JSON文件失败: ${error.message}`);
          // 继续删除元数据
        }
      }

      // 4. 更新元数据
      // 从元数据中删除记忆
      if (metadata.memory_metadata && metadata.memory_metadata[memoryId]) {
        delete metadata.memory_metadata[memoryId];
      }

      // 从块中删除记忆
      if (chunkId) {
        try {
          await this._removeMemoryFromChunk(chunkId, memoryId);

          // 更新元数据中的块信息
          const chunkIndex = metadata.memory_chunks.findIndex(c => c.id === chunkId);
          if (chunkIndex !== -1) {
            metadata.memory_chunks[chunkIndex].count = Math.max(0, metadata.memory_chunks[chunkIndex].count - 1);
          }

          // 删除记忆ID到块的映射
          delete metadata.memory_id_to_chunk[memoryId];
        } catch (error) {
          console.error(`从块中删除记忆失败: ${error.message}`);
          // 继续删除元数据
        }
      }

      // 更新记忆计数
      metadata.memory_count = Math.max(0, metadata.memory_count - 1);

      // 从最近记忆列表中删除
      metadata.memory_index.recent = metadata.memory_index.recent.filter(id => id !== memoryId);

      // 从分类索引中删除
      if (memoryMeta.category) {
        if (metadata.memory_index.by_category[memoryMeta.category]) {
          if (typeof metadata.memory_index.by_category[memoryMeta.category] === 'object' &&
              metadata.memory_index.by_category[memoryMeta.category].count !== undefined) {
            // 新结构
            metadata.memory_index.by_category[memoryMeta.category].count =
              Math.max(0, metadata.memory_index.by_category[memoryMeta.category].count - 1);
          } else if (Array.isArray(metadata.memory_index.by_category[memoryMeta.category])) {
            // 旧结构
            metadata.memory_index.by_category[memoryMeta.category] =
              metadata.memory_index.by_category[memoryMeta.category].filter(id => id !== memoryId);
          }
        }
      }

      // 从标签索引中删除
      if (memoryMeta.tags && Array.isArray(memoryMeta.tags)) {
        for (const tag of memoryMeta.tags) {
          if (metadata.memory_index.by_tag[tag]) {
            if (typeof metadata.memory_index.by_tag[tag] === 'object' &&
                metadata.memory_index.by_tag[tag].count !== undefined) {
              // 新结构
              metadata.memory_index.by_tag[tag].count =
                Math.max(0, metadata.memory_index.by_tag[tag].count - 1);

              // 如果标签下没有记忆了，删除该标签
              if (metadata.memory_index.by_tag[tag].count === 0) {
                delete metadata.memory_index.by_tag[tag];
              }
            } else if (Array.isArray(metadata.memory_index.by_tag[tag])) {
              // 旧结构
              metadata.memory_index.by_tag[tag] =
                metadata.memory_index.by_tag[tag].filter(id => id !== memoryId);

              // 如果标签下没有记忆了，删除该标签
              if (metadata.memory_index.by_tag[tag].length === 0) {
                delete metadata.memory_index.by_tag[tag];
              }
            }
          }
        }
      }

      // 保存更新后的元数据
      await this.updateUserMetadata(metadata);

      // 5. 从本地缓存中删除
      await this._removeMemoryFromCache(memoryId);

      // 6. 从搜索索引中删除
      try {
        await searchIndexService.removeMemoryFromIndex(memoryId);
        console.log(`记忆 ${memoryId} 已从搜索索引中删除`);
      } catch (indexError) {
        console.error(`从搜索索引中删除记忆失败: ${indexError.message}`);
        // 不阻止记忆的删除操作
      }

      console.log(`记忆删除成功: ${memoryId}`);
      return true;
    } catch (error) {
      console.error(`删除记忆失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 检查元数据是否过期
   * @param {Object} metadata - 元数据
   * @returns {boolean} 是否过期
   */
  _isMetadataExpired(metadata) {
    if (!metadata || !metadata.last_sync) return true;

    const lastSync = new Date(metadata.last_sync);
    const now = new Date();

    // 如果上次同步超过1小时，认为已过期
    return (now - lastSync) > 3600000;
  }

  /**
   * 创建或更新分类索引文件
   * @param {string} categoryId - 分类 ID
   * @param {Array<string>} memoryIds - 记忆 ID 数组
   * @returns {Promise<void>}
   * @private
   */
  async _createOrUpdateCategoryIndex(categoryId, memoryIds) {
    try {
      console.log(`创建或更新分类索引: ${categoryId}, 记忆数量: ${memoryIds.length}`);

      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 检查分类是否存在
      if (!metadata.memory_index.by_category[categoryId]) {
        metadata.memory_index.by_category[categoryId] = {
          count: 0,
          file: `indexes/categories/category_${categoryId}.json`
        };

        // 更新元数据
        await this.updateUserMetadata(metadata);
      }

      // 获取分类索引路径
      const indexPath = metadata.memory_index.by_category[categoryId].file;

      // 创建索引数据
      const indexData = {
        category_id: categoryId,
        count: memoryIds.length,
        last_updated: new Date().toISOString(),
        memories: memoryIds
      };

      // 保存索引文件
      await this.putObject(`users/${this.userId}/${indexPath}`, JSON.stringify(indexData));

      // 更新元数据中的计数
      metadata.memory_index.by_category[categoryId].count = memoryIds.length;
      await this.updateUserMetadata(metadata);

      console.log(`分类索引更新成功: ${categoryId}`);
    } catch (error) {
      console.error(`创建或更新分类索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 创建或更新标签索引文件
   * @param {string} tag - 标签
   * @param {Array<string>} memoryIds - 记忆 ID 数组
   * @returns {Promise<void>}
   * @private
   */
  async _createOrUpdateTagIndex(tag, memoryIds) {
    try {
      console.log(`创建或更新标签索引: ${tag}, 记忆数量: ${memoryIds.length}`);

      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 生成安全的文件名
      const safeTag = tag.replace(/[^a-zA-Z0-9]/g, '_');

      // 检查标签是否存在
      if (!metadata.memory_index.by_tag[tag]) {
        metadata.memory_index.by_tag[tag] = {
          count: 0,
          file: `indexes/tags/tag_${safeTag}.json`
        };

        // 更新元数据
        await this.updateUserMetadata(metadata);
      }

      // 获取标签索引路径
      const indexPath = metadata.memory_index.by_tag[tag].file;

      // 创建索引数据
      const indexData = {
        tag: tag,
        count: memoryIds.length,
        last_updated: new Date().toISOString(),
        memories: memoryIds
      };

      // 保存索引文件
      await this.putObject(`users/${this.userId}/${indexPath}`, JSON.stringify(indexData));

      // 更新元数据中的计数
      metadata.memory_index.by_tag[tag].count = memoryIds.length;
      await this.updateUserMetadata(metadata);

      console.log(`标签索引更新成功: ${tag}`);
    } catch (error) {
      console.error(`创建或更新标签索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 将旧的元数据结构迁移到新的分块结构
   * @returns {Promise<void>}
   */
  async migrateToChunkedStructure() {
    try {
      console.log('开始将旧的元数据结构迁移到新的分块结构...');

      // 获取元数据
      const metadata = await this.getUserMetadata();

      // 检查是否需要迁移
      if (!metadata.memory_metadata || Object.keys(metadata.memory_metadata).length === 0) {
        console.log('没有旧的元数据需要迁移');
        return;
      }

      // 初始化新结构
      if (!metadata.memory_chunks) {
        metadata.memory_chunks = [];
      }

      if (!metadata.memory_id_to_chunk) {
        metadata.memory_id_to_chunk = {};
      }

      // 按日期对记忆进行分组
      const memoryGroups = {};

      // 处理所有记忆
      for (const memoryId in metadata.memory_metadata) {
        const memoryMeta = metadata.memory_metadata[memoryId];

        // 如果记忆已经在新结构中，跳过
        if (metadata.memory_id_to_chunk[memoryId]) {
          continue;
        }

        // 获取记忆创建日期
        const createdAt = new Date(memoryMeta.created_at);
        const year = createdAt.getFullYear();
        const month = createdAt.getMonth();

        // 创建年月键
        const yearMonthKey = `${year}-${month}`;

        // 将记忆添加到相应的年月组
        if (!memoryGroups[yearMonthKey]) {
          memoryGroups[yearMonthKey] = {
            startDate: new Date(year, month, 1),
            endDate: new Date(year, month + 1, 0, 23, 59, 59, 999),
            memories: {}
          };
        }

        memoryGroups[yearMonthKey].memories[memoryId] = memoryMeta;
      }

      // 为每个年月组创建块
      for (const yearMonthKey in memoryGroups) {
        const group = memoryGroups[yearMonthKey];

        // 创建块ID
        const chunkId = `chunk_${yearMonthKey}_${Date.now()}`;

        // 创建块
        const chunk = await this._createMemoryChunk(chunkId, group.startDate, group.endDate);

        // 将记忆添加到块
        chunk.memories = group.memories;
        chunk.memory_count = Object.keys(group.memories).length;

        // 更新块
        await this._updateMemoryChunk(chunkId, chunk);

        // 更新元数据
        metadata.memory_chunks.push({
          id: chunkId,
          file: `chunks/chunk_${chunkId}.json`,
          count: chunk.memory_count,
          date_range: [group.startDate.toISOString(), group.endDate.toISOString()]
        });

        // 更新记忆ID到块的映射
        for (const memoryId in group.memories) {
          metadata.memory_id_to_chunk[memoryId] = chunkId;
        }

        console.log(`创建块 ${chunkId} 成功，包含 ${chunk.memory_count} 条记忆`);
      }

      // 更新元数据
      metadata.chunk_count = metadata.memory_chunks.length;
      await this.updateUserMetadata(metadata);

      // 迁移分类和标签索引
      await this._migrateIndexes(metadata);

      console.log('迁移完成，共创建了 ' + metadata.chunk_count + ' 个块');
    } catch (error) {
      console.error(`迁移元数据结构失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 迁移分类和标签索引
   * @param {Object} metadata - 元数据
   * @returns {Promise<void>}
   * @private
   */
  async _migrateIndexes(metadata) {
    try {
      console.log('开始迁移分类和标签索引...');

      // 迁移分类索引
      for (const categoryId in metadata.memory_index.by_category) {
        const categoryIndex = metadata.memory_index.by_category[categoryId];

        // 如果已经是新结构，跳过
        if (typeof categoryIndex === 'object' && categoryIndex.file) {
          continue;
        }

        // 创建新的分类索引
        await this._createOrUpdateCategoryIndex(categoryId, categoryIndex);

        // 更新元数据中的分类索引
        metadata.memory_index.by_category[categoryId] = {
          count: categoryIndex.length,
          file: `indexes/categories/category_${categoryId}.json`
        };
      }

      // 迁移标签索引
      for (const tag in metadata.memory_index.by_tag) {
        const tagIndex = metadata.memory_index.by_tag[tag];

        // 如果已经是新结构，跳过
        if (typeof tagIndex === 'object' && tagIndex.file) {
          continue;
        }

        // 创建新的标签索引
        await this._createOrUpdateTagIndex(tag, tagIndex);

        // 更新元数据中的标签索引
        const safeTag = tag.replace(/[^a-zA-Z0-9]/g, '_');
        metadata.memory_index.by_tag[tag] = {
          count: tagIndex.length,
          file: `indexes/tags/tag_${safeTag}.json`
        };
      }

      // 更新元数据
      await this.updateUserMetadata(metadata);

      console.log('分类和标签索引迁移完成');
    } catch (error) {
      console.error(`迁移分类和标签索引失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 合并元数据
   * @param {Object} localMetadata - 本地元数据
   * @param {Object} cloudMetadata - 云端元数据
   * @returns {Promise<Object>} 合并后的元数据
   * @private
   */
  async _mergeMetadata(localMetadata, cloudMetadata) {
    try {
      console.log('开始合并元数据...');

      // 如果本地元数据不存在，直接返回云端元数据
      if (!localMetadata) {
        console.log('本地元数据不存在，直接使用云端元数据');
        return cloudMetadata;
      }

      // 创建合并后的元数据对象，基于云端元数据
      const mergedMetadata = { ...cloudMetadata };

      // 合并记忆元数据
      // 策略：保留两边都有的记忆，以最后修改时间较新的为准
      for (const memoryId in localMetadata.memory_metadata) {
        // 跳过已删除的记忆
        if (cloudMetadata.deleted_memories && cloudMetadata.deleted_memories[memoryId]) {
          console.log(`跳过已删除的记忆: ${memoryId}`);
          continue;
        }

        const localMemory = localMetadata.memory_metadata[memoryId];
        const cloudMemory = cloudMetadata.memory_metadata[memoryId];

        // 如果云端没有这个记忆，或者本地版本更新，则使用本地版本
        if (!cloudMemory ||
            (localMemory.version && cloudMemory.version && localMemory.version > cloudMemory.version) ||
            (!cloudMemory.version && localMemory.version) ||
            (new Date(localMemory.lastModified || localMemory.createdAt) > new Date(cloudMemory.lastModified || cloudMemory.createdAt))) {
          console.log(`使用本地记忆数据: ${memoryId}`);
          mergedMetadata.memory_metadata[memoryId] = localMemory;

          // 同时更新索引
          this._updateMemoryIndexes(mergedMetadata, memoryId, localMemory);
        }
      }

      // 更新记忆计数
      mergedMetadata.memory_count = Object.keys(mergedMetadata.memory_metadata).length;

      // 合并设备信息
      mergedMetadata.devices = mergedMetadata.devices || {};
      if (localMetadata.devices) {
        for (const deviceId in localMetadata.devices) {
          if (!mergedMetadata.devices[deviceId]) {
            mergedMetadata.devices[deviceId] = localMetadata.devices[deviceId];
          }
        }
      }

      // 添加当前设备信息
      const deviceId = storageService.getDeviceId();

      // 检测设备类型
      let deviceType = 'chrome';
      let deviceName = 'Chrome 浏览器';

      if (deviceId.startsWith('firefox_')) {
        deviceType = 'firefox';
        deviceName = 'Firefox 浏览器';
      } else if (deviceId.startsWith('safari_')) {
        deviceType = 'safari';
        deviceName = 'Safari 浏览器';
      } else if (deviceId.startsWith('edge_')) {
        deviceType = 'edge';
        deviceName = 'Edge 浏览器';
      } else if (deviceId.startsWith('opera_')) {
        deviceType = 'opera';
        deviceName = 'Opera 浏览器';
      }

      // 获取操作系统信息
      let osInfo = 'Unknown';
      if (navigator.userAgent.indexOf('Windows') !== -1) {
        osInfo = 'Windows';
      } else if (navigator.userAgent.indexOf('Mac') !== -1) {
        osInfo = 'macOS';
      } else if (navigator.userAgent.indexOf('Linux') !== -1) {
        osInfo = 'Linux';
      } else if (navigator.userAgent.indexOf('Android') !== -1) {
        osInfo = 'Android';
      } else if (navigator.userAgent.indexOf('iOS') !== -1 || navigator.userAgent.indexOf('iPhone') !== -1 || navigator.userAgent.indexOf('iPad') !== -1) {
        osInfo = 'iOS';
      }

      mergedMetadata.devices[deviceId] = {
        last_active: new Date().toISOString(),
        device_type: deviceType,
        device_name: deviceName,
        os: osInfo,
        user_agent: navigator.userAgent
      };

      // 更新版本号
      mergedMetadata.version = Math.max(cloudMetadata.version || 0, localMetadata.version || 0) + 1;
      mergedMetadata.last_sync = new Date().toISOString();
      mergedMetadata.last_modified_by = deviceId;

      console.log(`元数据合并完成，新版本: ${mergedMetadata.version}`);
      return mergedMetadata;
    } catch (error) {
      console.error('合并元数据失败:', error);
      throw error;
    }
  }

  /**
   * 更新记忆索引
   * @param {Object} metadata - 元数据
   * @param {string} memoryId - 记忆ID
   * @param {Object} memory - 记忆对象
   * @private
   */
  _updateMemoryIndexes(metadata, memoryId, memory) {
    // 更新最近记忆索引
    if (!metadata.memory_index.recent.includes(memoryId)) {
      metadata.memory_index.recent.unshift(memoryId);
      // 保持最近记忆列表不超过100条
      if (metadata.memory_index.recent.length > 100) {
        metadata.memory_index.recent = metadata.memory_index.recent.slice(0, 100);
      }
    }

    // 更新分类索引
    if (memory.category) {
      if (!metadata.memory_index.by_category[memory.category]) {
        metadata.memory_index.by_category[memory.category] = [];
      }
      if (!metadata.memory_index.by_category[memory.category].includes(memoryId)) {
        metadata.memory_index.by_category[memory.category].push(memoryId);
      }
    }

    // 更新标签索引
    if (memory.tags && memory.tags.length > 0) {
      for (const tag of memory.tags) {
        if (!metadata.memory_index.by_tag[tag]) {
          metadata.memory_index.by_tag[tag] = [];
        }
        if (!metadata.memory_index.by_tag[tag].includes(memoryId)) {
          metadata.memory_index.by_tag[tag].push(memoryId);
        }
      }
    }

    // 更新收藏索引
    if (memory.favorite && !metadata.memory_index.favorites.includes(memoryId)) {
      metadata.memory_index.favorites.push(memoryId);
    } else if (!memory.favorite && metadata.memory_index.favorites.includes(memoryId)) {
      metadata.memory_index.favorites = metadata.memory_index.favorites.filter(id => id !== memoryId);
    }
  }

  /**
   * 上传对象到OSS
   * @param {string} key - 对象键
   * @param {string|Blob|File} body - 对象内容
   * @param {string} contentType - 内容类型
   * @param {Object} [metadata] - 元数据
   * @returns {Promise<Object>} 上传结果
   */
  async putObject(key, body, contentType = 'application/json', metadata = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    try {
      // 使用抽象接口上传对象
      return await this.provider.putObject(key, body, contentType, metadata);
    } catch (error) {
      console.error(`上传对象失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 从 OSS 删除对象
   * @param {string} key - 对象键
   * @returns {Promise<Object>} 删除结果
   */
  async deleteObject(key) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    try {
      // 使用抽象接口删除对象
      const result = await this.provider.deleteObject(key);
      console.log(`删除对象 ${key} 成功`);
      return result;
    } catch (error) {
      console.error(`删除对象 ${key} 失败:`, error);
      throw error;
    }
  }

  /**
   * 列出 OSS 中的对象
   * @param {string} prefix - 前缀
   * @param {Object} options - 其他选项
   * @returns {Promise<Array>} 对象列表
   */
  async listObjects(prefix, options = {}) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    try {
      console.log(`列出对象: prefix=${prefix}`);

      // 准备选项
      const listOptions = {
        prefix,
        ...options
      };

      // 使用抽象接口列出对象
      const result = await this.provider.listObjects(listOptions);

      // 处理不同提供者的返回结果格式
      let objects = [];

      console.log('列出对象原始结果:', JSON.stringify(result).substring(0, 500) + '...');

      if (result && result.objects && Array.isArray(result.objects)) {
        // 已经标准化的格式
        objects = result.objects;
      } else if (result && result.result && result.result.InterfaceResult && Array.isArray(result.result.InterfaceResult.Contents)) {
        // 华为 OBS 格式
        objects = result.result.InterfaceResult.Contents.map(item => ({
          Key: item.Key || '',
          Size: item.Size || 0,
          LastModified: item.LastModified || new Date().toISOString(),
          ETag: item.ETag || ''
        }));
      } else if (result && result.InterfaceResult && Array.isArray(result.InterfaceResult.Contents)) {
        // 另一种华为 OBS 格式
        objects = result.InterfaceResult.Contents.map(item => ({
          Key: item.Key || '',
          Size: item.Size || 0,
          LastModified: item.LastModified || new Date().toISOString(),
          ETag: item.ETag || ''
        }));
      } else if (result && Array.isArray(result)) {
        // MinIO 可能直接返回数组
        objects = result.map(item => ({
          Key: item.name || item.Name || item.key || item.Key || '',
          Size: item.size || item.Size || 0,
          LastModified: item.lastModified || item.LastModified || new Date().toISOString(),
          ETag: item.etag || item.ETag || ''
        }));
      } else {
        console.warn('无法识别的对象列表格式:', result);
        // 尝试从结果中提取任何可能的对象列表
        if (result && typeof result === 'object') {
          // 尝试找到任何数组属性
          for (const key in result) {
            if (Array.isArray(result[key])) {
              console.log(`尝试使用 ${key} 属性作为对象列表`);
              objects = result[key].map(item => ({
                Key: item.name || item.Name || item.key || item.Key || '',
                Size: item.size || item.Size || 0,
                LastModified: item.lastModified || item.LastModified || new Date().toISOString(),
                ETag: item.etag || item.ETag || ''
              }));
              break;
            }
          }
        }
      }

      console.log(`列出对象成功，找到 ${objects.length} 个对象`);
      return objects;
    } catch (error) {
      console.error(`列出对象失败:`, error);
      throw error;
    }
  }

  /**
   * 从OSS获取对象
   * @param {string} key - 对象键
   * @param {Object} [options] - 获取选项
   * @returns {Promise<Object>} 对象内容
   */
  async getObject(key, options = {}) {
    try {
      console.log(`开始从OSS获取对象: ${key}`);

      if (!this.isInitialized) {
        console.log('OSS存储服务未初始化，正在初始化...');
        await this.initialize();
      }

      if (!this.isInitialized) {
        throw new Error('OSS存储服务初始化失败');
      }

      if (!this.provider) {
        throw new Error('OSS存储提供者不存在');
      }

      console.log(`调用存储提供者获取对象: ${key}`);

      try {
        // 使用抽象接口获取对象
        const result = await this.provider.getObject(key, options);

        // 处理不同提供者的返回结果格式
        if (result && result.InterfaceResult && result.InterfaceResult.Content) {
          // 华为 OBS 格式
          try {
            // 尝试解析JSON
            const content = result.InterfaceResult.Content;
            const data = JSON.parse(content);
            console.log(`成功解析对象${key}的JSON内容`);
            return data;
          } catch (parseError) {
            console.error(`解析对象${key}的JSON内容失败:`, parseError);
            // 如果不是JSON，返回原始内容
            return result.InterfaceResult.Content;
          }
        } else if (result instanceof ArrayBuffer || result instanceof Uint8Array) {
          // ArrayBuffer 或 Uint8Array 格式
          try {
            // 尝试解析JSON
            const decoder = new TextDecoder('utf-8');
            const content = decoder.decode(result);
            const data = JSON.parse(content);
            console.log(`成功解析对象${key}的JSON内容`);
            return data;
          } catch (parseError) {
            console.error(`解析对象${key}的JSON内容失败:`, parseError);
            // 如果不是JSON，返回原始内容
            return result;
          }
        } else {
          // 其他格式
          return result;
        }
      } catch (error) {
        console.error(`获取对象${key}失败:`, error);
        throw error;
      }
    } catch (error) {
      console.error(`获取对象${key}失败:`, error);
      throw error;
    }
  }

  /**
   * 获取对象的签名URL
   * @param {string} key - 对象键
   * @param {number} expires - 过期时间（秒）
   * @returns {Promise<string>} 签名URL
   */
  async getSignedUrl(key, expires = 3600) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log("getSignedUrl key:", key)

    if (!this.isInitialized) {
      throw new Error('OSS存储服务未初始化');
    }

    const method = 'GET';

    var res = this.provider.createSignedUrlSync({
      Method: method,
      Bucket: this.bucketName,
      Key: key,
      Expires: expires
    });

    // 如果是 Promise，等待它解析（MinIO提供者使用AWS SDK的情况）
    if (res instanceof Promise) {
      try {
        const signedUrl = await res;
        console.log("getSignedUrl result (Promise):", signedUrl);
        return signedUrl;
      } catch (error) {
        console.error("解析签名URL Promise失败:", error);
        return `${this.baseUrl}/${key}`;
      }
    }

    // 如果是字符串，直接返回（旧版MinIO提供者的情况）
    if (typeof res === 'string') {
      console.log("getSignedUrl result (string):", res);
      return res;
    }

    // 如果是对象，处理华为云OBS的情况
    const reopt = {
      method : method,
      url : res.SignedUrl,
      withCredentials: false,
      headers : res.ActualSignedRequestHeaders || {},
      validateStatus: function(status){
        return status >= 200;
      },
      maxRedirects : 0,
      responseType : 'text',
    };

    console.log("################4.1.3:", reopt)
    return reopt.url;
  }
}

// 导出单例实例
const ossStorageService = new OssStorageService();
export default ossStorageService;
