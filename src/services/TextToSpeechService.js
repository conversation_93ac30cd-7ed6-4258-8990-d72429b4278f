/**
 * 文本转语音服务
 * 提供将文本转换为语音的功能
 */
class TextToSpeechService {
  constructor() {
    this.apiKey = '';
    this.apiEndpoint = 'https://api.openai.com/v1/audio/speech';
    this.isPlaying = false;
    this.audio = null;
    this.currentText = '';
    this.voice = 'alloy'; // 默认语音：alloy, echo, fable, onyx, nova, shimmer
    this.model = 'tts-1'; // 默认模型：tts-1, tts-1-hd
    this.speed = 1.0; // 默认语速：0.25-4.0
  }

  /**
   * 初始化服务
   * @param {Object} config 配置对象
   * @returns {Promise<boolean>} 是否初始化成功
   */
  async initialize(config = {}) {
    try {
      // 从存储中获取API密钥和其他设置
      const { apiKey, voice, model, speed } = config;
      
      if (apiKey) {
        this.apiKey = apiKey;
      } else {
        // 从存储中获取API密钥
        const settings = await this.getSettings();
        if (settings && settings.ttsSettings && settings.ttsSettings.apiKey) {
          this.apiKey = settings.ttsSettings.apiKey;
        }
      }
      
      // 设置语音参数
      if (voice) this.voice = voice;
      if (model) this.model = model;
      if (speed) this.speed = speed;
      
      return !!this.apiKey;
    } catch (error) {
      console.error('初始化文本转语音服务失败:', error);
      return false;
    }
  }

  /**
   * 从存储中获取设置
   * @returns {Promise<Object>} 设置对象
   */
  async getSettings() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['settings'], (result) => {
        resolve(result.settings || {});
      });
    });
  }

  /**
   * 保存设置到存储
   * @param {Object} settings 设置对象
   * @returns {Promise<void>}
   */
  async saveSettings(settings) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ settings }, resolve);
    });
  }

  /**
   * 将文本转换为语音
   * @param {string} text 要转换的文本
   * @returns {Promise<string>} 音频URL
   */
  async textToSpeech(text) {
    if (!this.apiKey) {
      throw new Error('API密钥未设置，请在设置中配置API密钥');
    }

    if (!text || text.trim() === '') {
      throw new Error('文本不能为空');
    }

    try {
      this.currentText = text;
      
      // 准备请求数据
      const requestData = {
        model: this.model,
        input: text,
        voice: this.voice,
        speed: this.speed,
        response_format: 'mp3'
      };

      // 发送API请求
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API请求失败: ${response.status} ${response.statusText} ${JSON.stringify(errorData)}`);
      }

      // 获取音频数据
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      return audioUrl;
    } catch (error) {
      console.error('文本转语音失败:', error);
      throw error;
    }
  }

  /**
   * 播放文本
   * @param {string} text 要播放的文本
   * @returns {Promise<void>}
   */
  async playText(text) {
    try {
      // 如果已经在播放，先停止
      if (this.isPlaying) {
        this.stop();
      }

      // 转换文本为语音
      const audioUrl = await this.textToSpeech(text);
      
      // 创建音频元素并播放
      this.audio = new Audio(audioUrl);
      
      // 设置事件监听器
      this.audio.onended = () => {
        this.isPlaying = false;
        this.audio = null;
      };
      
      this.audio.onerror = (error) => {
        console.error('音频播放错误:', error);
        this.isPlaying = false;
        this.audio = null;
      };
      
      // 播放音频
      await this.audio.play();
      this.isPlaying = true;
    } catch (error) {
      console.error('播放文本失败:', error);
      throw error;
    }
  }

  /**
   * 停止播放
   */
  stop() {
    if (this.audio) {
      this.audio.pause();
      this.audio = null;
    }
    this.isPlaying = false;
  }

  /**
   * 暂停播放
   */
  pause() {
    if (this.audio) {
      this.audio.pause();
      this.isPlaying = false;
    }
  }

  /**
   * 恢复播放
   */
  resume() {
    if (this.audio) {
      this.audio.play();
      this.isPlaying = true;
    }
  }

  /**
   * 设置语音
   * @param {string} voice 语音名称
   */
  setVoice(voice) {
    this.voice = voice;
  }

  /**
   * 设置模型
   * @param {string} model 模型名称
   */
  setModel(model) {
    this.model = model;
  }

  /**
   * 设置语速
   * @param {number} speed 语速
   */
  setSpeed(speed) {
    this.speed = speed;
  }

  /**
   * 设置API密钥
   * @param {string} apiKey API密钥
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  /**
   * 检查是否正在播放
   * @returns {boolean} 是否正在播放
   */
  isCurrentlyPlaying() {
    return this.isPlaying;
  }

  /**
   * 获取当前播放的文本
   * @returns {string} 当前播放的文本
   */
  getCurrentText() {
    return this.currentText;
  }
}

// 创建单例实例
const textToSpeechService = new TextToSpeechService();

export default textToSpeechService;
