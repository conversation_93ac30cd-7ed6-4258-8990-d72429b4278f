import React, { useState, useEffect } from 'react';
import {
  Layout,
  Typography,
  Form,
  Input,
  Button,
  Select,
  Upload,
  message,
  Tag,
  Divider,
  Result,
  Space,
  Alert
} from 'antd';
import {
  ArrowLeftOutlined,
  SendOutlined,
  UploadOutlined,
  GithubOutlined,
  BugOutlined,
  BulbOutlined,
  QuestionOutlined,
  ReadOutlined
} from '@ant-design/icons';

const { Header, Content } = Layout;
const { Title, Paragraph, Text, Link } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 问题反馈组件
 * 用于收集用户反馈和建议
 */
const Feedback = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [feedbackType, setFeedbackType] = useState('bug');
  const [browserInfo, setBrowserInfo] = useState({
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    appVersion: chrome.runtime.getManifest().version
  });

  // 返回上一页
  const goBack = () => {
    // 如果在iframe中，则发送消息到父窗口
    if (window.parent !== window) {
      window.parent.postMessage({
        type: 'navigate',
        data: { page: 'options' }
      }, '*');
    } else {
      window.close();
    }
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    setLoading(true);

    try {
      // 准备要提交的数据
      const formData = new FormData();

      // 添加表单字段
      formData.append('feedbackType', feedbackType);
      formData.append('title', values.title);
      formData.append('description', values.description);
      formData.append('email', values.email || '未提供');

      // 添加浏览器信息
      formData.append('browserInfo', JSON.stringify(browserInfo));

      // 添加截图（如果有）
      if (values.screenshot && values.screenshot.fileList && values.screenshot.fileList.length > 0) {
        const file = values.screenshot.fileList[0].originFileObj;
        formData.append('screenshot', file);
      }

      // 使用Formspree发送表单数据
      // 注意：您需要将下面的URL替换为您自己的Formspree表单URL
      const response = await fetch('https://formspree.io/f/mqaqeepz', {
        method: 'POST',
        body: formData,
        headers: {
          'Accept': 'application/json'
        }
      });

      if (response.ok) {
        message.success('反馈已成功提交，感谢您的宝贵意见！');
        setSubmitted(true);
        form.resetFields();
      } else {
        console.log(response)
        throw new Error('提交失败，请稍后再试');
      }
    } catch (error) {
      console.error('提交反馈失败:', error);
      message.error('提交失败，请尝试使用GitHub Issues或邮件方式反馈');
    } finally {
      setLoading(false);
    }
  };

  // 处理反馈类型变更
  const handleFeedbackTypeChange = (type) => {
    setFeedbackType(type);
    form.setFieldsValue({ feedbackType: type });
  };

  // 提交新的反馈
  const submitNewFeedback = () => {
    setSubmitted(false);
    form.resetFields();
  };

  // 渲染反馈表单
  const renderFeedbackForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{ feedbackType }}
      className="feedback-form"
    >
      <Form.Item name="feedbackType" hidden>
        <Input />
      </Form.Item>

      <div style={{ marginBottom: 16 }}>
        <Space>
          <Tag
            icon={<BugOutlined />}
            color={feedbackType === 'bug' ? 'blue' : 'default'}
            onClick={() => handleFeedbackTypeChange('bug')}
            className={`feedback-type-tag ${feedbackType === 'bug' ? 'selected' : ''}`}
          >
            问题反馈
          </Tag>
          <Tag
            icon={<BulbOutlined />}
            color={feedbackType === 'feature' ? 'blue' : 'default'}
            onClick={() => handleFeedbackTypeChange('feature')}
            className={`feedback-type-tag ${feedbackType === 'feature' ? 'selected' : ''}`}
          >
            功能建议
          </Tag>
          <Tag
            icon={<QuestionOutlined />}
            color={feedbackType === 'question' ? 'blue' : 'default'}
            onClick={() => handleFeedbackTypeChange('question')}
            className={`feedback-type-tag ${feedbackType === 'question' ? 'selected' : ''}`}
          >
            使用咨询
          </Tag>
        </Space>
      </div>

      <Form.Item
        name="title"
        label={<span style={{ fontSize: '14px' }}>标题</span>}
        rules={[{ required: true, message: '请输入反馈标题' }]}
      >
        <Input placeholder={feedbackType === 'bug' ? '请简要描述您遇到的问题' : feedbackType === 'feature' ? '请简要描述您的功能建议' : '请简要描述您的问题'} />
      </Form.Item>

      <Form.Item
        name="description"
        label={<span style={{ fontSize: '14px' }}>详细描述</span>}
        rules={[{ required: true, message: '请输入详细描述' }]}
      >
        <TextArea
          rows={6}
          placeholder={
            feedbackType === 'bug'
              ? '请详细描述问题发生的步骤、预期行为和实际行为'
              : feedbackType === 'feature'
                ? '请详细描述您希望添加的功能及其使用场景'
                : '请详细描述您的问题或疑问'
          }
        />
      </Form.Item>

      <Form.Item
        name="email"
        label={<span style={{ fontSize: '14px' }}>联系方式 <span className="feedback-contact-optional">(选填，用于回复您的反馈)</span></span>}
      >
        <Input placeholder="您的邮箱地址" />
      </Form.Item>

      <Alert
        message="提示：由于我们使用的是免费的表单服务，目前暂不支持上传截图。如需提供截图，请使用GitHub Issues或发送邮件。"
        type="info"
        showIcon
        style={{ marginBottom: 16, fontSize: '14px' }}
      />

      <div className="feedback-browser-info">
        <Title level={5} style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#666' }}>系统信息</Title>
        <div className="feedback-browser-info-item">浏览器: {browserInfo.userAgent}</div>
        <div className="feedback-browser-info-item">平台: {browserInfo.platform}</div>
        <div className="feedback-browser-info-item">扩展版本: {browserInfo.appVersion}</div>
      </div>

      <Form.Item className="feedback-submit-btn">
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          icon={<SendOutlined />}
          size="large"
          style={{ fontSize: '14px', height: 'auto', padding: '6px 16px' }}
        >
          提交反馈
        </Button>
      </Form.Item>

      <Divider className="feedback-divider">或者</Divider>

      <div className="feedback-alternative">
        <Paragraph style={{ fontSize: '14px', marginBottom: '16px' }}>
          您也可以通过以下方式提交反馈：
        </Paragraph>
        <Space direction="vertical">
          <Button
            type="default"
            icon={<GithubOutlined />}
            onClick={() => window.open('https://github.com/EverSnip/EverSnip/issues/new', '_blank')}
            style={{ fontSize: '14px', height: 'auto', padding: '6px 15px' }}
          >
            在GitHub上提交Issue
          </Button>
          <Button
            type="default"
            icon={<ReadOutlined />}
            onClick={() => window.open('https://eversnip.github.io/EverSnip/', '_blank')}
            style={{ fontSize: '14px', height: 'auto', padding: '6px 15px' }}
          >
            查看使用文档
          </Button>
          <Button
            type="link"
            onClick={() => window.location.href = 'mailto:<EMAIL>?subject=拾光忆栈反馈'}
            style={{ fontSize: '14px' }}
          >
            发送邮件至 <EMAIL>
          </Button>
        </Space>
      </div>
    </Form>
  );

  // 渲染提交成功页面
  const renderSuccessPage = () => (
    <Result
      status="success"
      title="反馈已成功提交！"
      subTitle="感谢您的宝贵意见，我们会认真考虑您的反馈。"
      extra={[
        <Button
          type="primary"
          key="new-feedback"
          onClick={submitNewFeedback}
          style={{ fontSize: '14px', height: 'auto', padding: '6px 15px' }}
        >
          提交新的反馈
        </Button>,
        <Button
          key="back"
          onClick={goBack}
          style={{ fontSize: '14px', height: 'auto', padding: '6px 15px' }}
        >返回</Button>,
      ]}
    />
  );

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', display: 'flex', alignItems: 'center' }}>
        <Button
          type="text"
          icon={<ArrowLeftOutlined />}
          onClick={goBack}
          style={{ marginRight: '16px' }}
        />
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img src="../icons/icon48.png" alt="Logo" style={{ width: 24, height: 24, marginRight: 8 }} />
          <Title level={3} style={{ margin: '0' }}>问题反馈</Title>
        </div>
      </Header>
      <Content className="feedback-container">
        {submitted ? renderSuccessPage() : (
          <>
            <Paragraph style={{ fontSize: '14px', marginBottom: '20px' }}>
              我们非常重视您的反馈和建议，它们对我们改进产品至关重要。请告诉我们您的想法、遇到的问题或功能建议。
            </Paragraph>

            {renderFeedbackForm()}
          </>
        )}
      </Content>
    </Layout>
  );
};

export default Feedback;
