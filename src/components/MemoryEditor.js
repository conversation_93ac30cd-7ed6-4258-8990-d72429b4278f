import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Select, Button, Upload, message, Space, Divider, Tag, DatePicker, Switch, TimePicker } from 'antd';
import { PlusOutlined, UploadOutlined, PictureOutlined, VideoCameraOutlined, SaveOutlined, DeleteOutlined, HistoryOutlined, CalendarOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { storageService, ossStorageService } from '../services';
import dayjs from 'dayjs';

const { TextArea } = Input;

/**
 * 记忆编辑组件
 * 用于添加和编辑记忆，支持文本、图片和视频
 */
const MemoryEditor = ({ memory, onSave, onCancel }) => {
  const [form] = Form.useForm();
  const [categories, setCategories] = useState([]);
  const [defaultCategory, setDefaultCategory] = useState('');
  const [loading, setLoading] = useState(false);
  const [imageList, setImageList] = useState([]);
  const [videoList, setVideoList] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [useHistoricalDate, setUseHistoricalDate] = useState(false);
  const [historicalDate, setHistoricalDate] = useState(dayjs());
  const dateFormat = 'YYYY-MM-DD';
  const timeFormat = 'HH:mm:ss';

  const uploadRef = useRef();

  // 加载分类和默认分类
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const settings = await storageService.getSettings();
        if (settings.categories) {
          setCategories(settings.categories);
        }

        // 加载默认分类
        if (settings.defaultCategory) {
          setDefaultCategory(settings.defaultCategory);

          // 如果表单已经初始化，更新分类字段
          form.setFieldsValue({ category: settings.defaultCategory });
        }

        console.log('加载分类和默认分类成功:', settings.categories, settings.defaultCategory);
      } catch (error) {
        console.error('加载分类失败:', error);
        message.error('加载分类失败');
      }
    };

    loadCategories();
  }, [form]);

  // 当默认分类变化时更新表单
  useEffect(() => {
    if (defaultCategory && !memory) {
      form.setFieldsValue({ category: defaultCategory });
    }
  }, [defaultCategory, form, memory]);

  // 获取图片base64的辅助函数
  const getImageBase64 = async (image) => {
    if (!image) return '';

    // 如果已经是base64格式，直接返回
    if (image.thumbnail_base64) return image.thumbnail_base64;

    // 如果有ID，使用getImageBase64获取base64
    if (image.id && memory.id && ossStorageService.isInitialized) {
      try {
        // 使用OSS服务获取base64
        const base64 = await ossStorageService.getImageBase64(memory.id, image.id, 'thumbnail');
        return base64;
      } catch (error) {
        console.error('获取图片base64失败:', error);
        return '';
      }
    }

    return '';
  };

  // 获取视频缩略图base64的辅助函数
  const getVideoBase64 = async (video) => {
    if (!video) return '';

    // 如果已经是base64格式，直接返回
    if (video.thumbnail_base64) return video.thumbnail_base64;

    // 如果有ID，使用getVideoThumbnailBase64获取base64
    if (video.id && memory.id && ossStorageService.isInitialized) {
      try {
        // 使用OSS服务获取base64
        const base64 = await ossStorageService.getVideoThumbnailBase64(memory.id, video.id);
        return base64;
      } catch (error) {
        console.error('获取视频缩略图base64失败:', error);
        return '';
      }
    }

    return '';
  };

  // 如果有记忆数据，填充表单
  useEffect(() => {
    if (memory) {
      form.setFieldsValue({
        title: memory.title,
        content: memory.content,
        category: memory.category,
        tags: memory.tags || []
      });

      // 如果记忆有历史日期，设置历史日期
      if (memory.historicalDate) {
        try {
          const histDate = dayjs(memory.historicalDate);
          if (histDate.isValid()) {
            setUseHistoricalDate(true);
            setHistoricalDate(histDate);
            console.log('加载历史日期:', histDate.format(dateFormat + ' ' + timeFormat));
          }
        } catch (error) {
          console.error('加载历史日期出错:', error);
        }
      }

      // 如果有图片，加载图片预览
      if (memory.images && memory.images.length > 0) {
        const loadImages = async () => {
          const images = await Promise.all(memory.images.map(async (image) => {
            // 获取图片URL
            const url = await getImageUrl(image);
            return {
              uid: image.id,
              name: image.filename,
              status: 'done',
              url: url,
              originFileObj: null,
              response: { id: image.id },
              // 保存原始图片数据以便于编辑
              originalData: image
            };
          }));
          setImageList(images);
        };
        loadImages();
      }

      // 如果有视频，加载视频预览
      if (memory.videos && memory.videos.length > 0) {
        const loadVideos = async () => {
          const videos = await Promise.all(memory.videos.map(async (video) => {
            // 获取视频缩略图URL
            const url = await getVideoUrl(video);
            return {
              uid: video.id,
              name: video.filename,
              status: 'done',
              url: url,
              originFileObj: null,
              response: { id: video.id },
              // 保存原始视频数据以便于编辑
              originalData: video
            };
          }));
          setVideoList(videos);
        };
        loadVideos();
      }
    }
  }, [memory, form]);

  // 保存记忆
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 收集图片文件
      const images = imageList
        .filter(file => file.originFileObj)
        .map(file => file.originFileObj);

      // 收集视频文件
      const videos = videoList
        .filter(file => file.originFileObj)
        .map(file => file.originFileObj);

      // 如果是编辑现有记忆
      if (memory && memory.id) {
        // TODO: 实现编辑现有记忆的逻辑
        message.success('记忆更新成功');
      } else {
        // 添加新记忆
        try {
          // 初始化OSS存储服务
          await ossStorageService.initialize();

          // 添加历史时间信息
          if (useHistoricalDate && historicalDate) {
            try {
              // 确保历史日期是有效的dayjs对象
              values.historicalDate = historicalDate.toDate();
              console.log('使用历史日期:', historicalDate.format(dateFormat + ' ' + timeFormat));
            } catch (error) {
              console.error('处理历史日期时出错:', error);
              // 出错时使用当前时间
              values.historicalDate = new Date();
            }
          }

          // 添加记忆
          const memoryId = await ossStorageService.addMemory(values, images, videos);
          console.log(values);

          message.success('记忆保存成功，正在跳转到浏览页面...');

          // 调用保存回调
          if (onSave) {
            onSave(memoryId);
          }
        } catch (error) {
          console.error('保存记忆失败:', error);
          message.error('保存记忆失败: ' + error.message);
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理历史日期开关变化
  const handleHistoricalDateChange = (checked) => {
    setUseHistoricalDate(checked);

    // 当开启历史日期时，确保有一个有效的日期
    if (checked) {
      // 重新初始化为当前时间
      setHistoricalDate(dayjs());
      console.log('开启历史日期，设置为当前时间:', dayjs().format(dateFormat + ' ' + timeFormat));
    }
  };

  // 处理历史日期选择变化
  const handleDateChange = (date) => {
    if (!date) return;

    try {
      // 创建一个新的dayjs对象
      console.log('日期变化:', date.format(dateFormat + ' ' + timeFormat));
      setHistoricalDate(date);
    } catch (error) {
      console.error('处理日期变化时出错:', error);
      setHistoricalDate(dayjs());
    }
  };

  // 处理图片上传前的验证
  const beforeImageUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return false;
    }

    const isLt20M = file.size / 1024 / 1024 < 20;
    if (!isLt20M) {
      message.error('图片必须小于20MB!');
      return false;
    }

    return true;
  };

  // 处理视频上传前的验证
  const beforeVideoUpload = (file) => {
    const isVideo = file.type.startsWith('video/');
    if (!isVideo) {
      message.error('只能上传视频文件!');
      return false;
    }

    const isLt500M = file.size / 1024 / 1024 < 500;
    if (!isLt500M) {
      message.error('视频必须小于500MB!');
      return false;
    }

    return true;
  };

  // 处理图片上传状态变化
  const handleImageChange = ({ fileList }) => {
    setImageList(fileList);
  };

  // 处理视频上传状态变化
  const handleVideoChange = ({ fileList }) => {
    setVideoList(fileList);
  };

  // 处理图片预览
  const handlePreview = async (file) => {
    // 如果是新上传的文件，使用Base64预览
    if (file.originFileObj && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }

    // 如果是已存在的文件，使用base64或者从原始数据获取
    if (!file.url && !file.preview && file.originalData) {
      // 如果是图片，使用getImageBase64获取base64
      if (file.originalData.id && memory.id && ossStorageService.isInitialized) {
        try {
          // 先尝试获取原始图片
          const base64 = await ossStorageService.getImageBase64(memory.id, file.originalData.id, 'original');
          file.preview = base64;
        } catch (error) {
          console.error('获取原始图片base64失败:', error);
          // 如果获取原始图片失败，回退到缩略图
          try {
            const base64 = await ossStorageService.getImageBase64(memory.id, file.originalData.id, 'thumbnail');
            file.preview = base64;
          } catch (innerError) {
            console.error('获取缩略图base64失败:', innerError);
          }
        }
      }
    }

    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(file.name || (file.url && file.url.substring(file.url.lastIndexOf('/') + 1)));
  };

  // 将文件转换为Base64
  const getBase64 = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });
  };

  // 自定义上传按钮
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传</div>
    </div>
  );

  return (
    <Form
      form={form}
      layout="vertical"
      key={`memory-form-${defaultCategory}`}
      initialValues={{
        title: '',
        content: '',
        category: defaultCategory || (categories.length > 0 ? categories[0].id : ''),
        tags: []
      }}
    >
      <Form.Item
        name="title"
        label="标题"
        rules={[{ required: true, message: '请输入记忆标题' }]}
      >
        <Input placeholder="输入记忆标题" />
      </Form.Item>

      <Form.Item
        name="content"
        label="内容"
        rules={[{ required: true, message: '请输入记忆内容' }]}
      >
        <TextArea
          placeholder="输入记忆内容"
          autoSize={{ minRows: 4, maxRows: 8 }}
        />
      </Form.Item>

      <Form.Item
        name="category"
        label="分类"
        rules={[{ required: true, message: '请选择分类' }]}
      >
        <Select placeholder="选择分类">
          {categories.map(category => (
            <Select.Option key={category.id} value={category.id}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  borderRadius: '50%',
                  backgroundColor: category.color,
                  marginRight: '8px'
                }} />
                {category.name}
              </div>
            </Select.Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item
        name="tags"
        label="标签"
      >
        <Select
          mode="tags"
          placeholder="输入标签，按回车确认"
          tokenSeparators={[',']}
        />
      </Form.Item>

      <Form.Item
        label={
          <span>
            <HistoryOutlined style={{ marginRight: 8 }} />
            使用历史时间
          </span>
        }
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Switch
            checked={useHistoricalDate}
            onChange={handleHistoricalDateChange}
          />
          {useHistoricalDate && (
            <div>
              <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                <CalendarOutlined style={{ marginRight: 8 }} />
                <span>选择历史时间：</span>
              </div>
              <DatePicker
                showTime={{ format: timeFormat }}
                value={historicalDate}
                onChange={handleDateChange}
                style={{ width: '100%' }}
                format={dateFormat + ' ' + timeFormat}
                allowClear={false}
                inputReadOnly
              />
            </div>
          )}
        </Space>
      </Form.Item>

      <Divider orientation="left">图片</Divider>

      <Form.Item label="上传图片">
        <Upload
          listType="picture-card"
          fileList={imageList}
          onPreview={handlePreview}
          onChange={handleImageChange}
          beforeUpload={beforeImageUpload}
          customRequest={({ file, onSuccess }) => {
            // 自定义上传，不实际上传，只是保存文件对象
            setTimeout(() => {
              onSuccess({ id: `temp_${Date.now()}` });
            }, 0);
          }}
        >
          {imageList.length >= 9 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <Divider orientation="left">视频</Divider>

      <Form.Item label="上传视频">
        <Upload
          listType="picture-card"
          fileList={videoList}
          onChange={handleVideoChange}
          beforeUpload={beforeVideoUpload}
          customRequest={({ file, onSuccess }) => {
            // 自定义上传，不实际上传，只是保存文件对象
            setTimeout(() => {
              onSuccess({ id: `temp_${Date.now()}` });
            }, 0);
          }}
        >
          {videoList.length >= 9 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <Divider />

      <Form.Item>
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={loading}
          >
            保存记忆
          </Button>

          {onCancel && (
            <Button onClick={onCancel}>
              取消
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  );
};

export default MemoryEditor;
