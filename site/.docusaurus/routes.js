import React from 'react';
import ComponentCreator from '@docusaurus/ComponentCreator';

export default [
  {
    path: '/MemoryKeeper/__docusaurus/debug',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug', '11d'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/config',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/config', '03d'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/content',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/content', 'a7c'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/globalData',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/globalData', '216'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/metadata',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/metadata', '16d'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/registry',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/registry', 'c18'),
    exact: true
  },
  {
    path: '/MemoryKeeper/__docusaurus/debug/routes',
    component: ComponentCreator('/MemoryKeeper/__docusaurus/debug/routes', '6ad'),
    exact: true
  },
  {
    path: '/MemoryKeeper/examples',
    component: ComponentCreator('/MemoryKeeper/examples', '10e'),
    exact: true
  },
  {
    path: '/MemoryKeeper/docs',
    component: ComponentCreator('/MemoryKeeper/docs', '124'),
    routes: [
      {
        path: '/MemoryKeeper/docs/about',
        component: ComponentCreator('/MemoryKeeper/docs/about', 'de5'),
        exact: true,
        sidebar: "tutorialSidebar"
      },
      {
        path: '/MemoryKeeper/docs/changelog',
        component: ComponentCreator('/MemoryKeeper/docs/changelog', '28d'),
        exact: true,
        sidebar: "tutorialSidebar"
      },
      {
        path: '/MemoryKeeper/docs/getting-started/installation',
        component: ComponentCreator('/MemoryKeeper/docs/getting-started/installation', '914'),
        exact: true,
        sidebar: "tutorialSidebar"
      },
      {
        path: '/MemoryKeeper/docs/support',
        component: ComponentCreator('/MemoryKeeper/docs/support', '6da'),
        exact: true,
        sidebar: "tutorialSidebar"
      }
    ]
  },
  {
    path: '/MemoryKeeper/',
    component: ComponentCreator('/MemoryKeeper/', '85a'),
    exact: true
  },
  {
    path: '*',
    component: ComponentCreator('*'),
  },
];
