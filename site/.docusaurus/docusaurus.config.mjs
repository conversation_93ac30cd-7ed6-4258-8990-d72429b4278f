/*
 * AUTOGENERATED - DON'T EDIT
 * Your edits in this file will be overwritten in the next build!
 * Modify the docusaurus.config.js file at your site's root instead.
 */
export default {
  "title": "拾光忆栈",
  "tagline": "让每一刻记忆都被珍藏",
  "favicon": "img/favicon.ico",
  "url": "https://aiplayzone.github.io",
  "baseUrl": "/MemoryKeeper/",
  "organizationName": "AIPlayZone",
  "projectName": "MemoryKeeper",
  "onBrokenLinks": "throw",
  "onBrokenMarkdownLinks": "warn",
  "i18n": {
    "defaultLocale": "zh-Hans",
    "locales": [
      "zh-Hans",
      "en"
    ],
    "localeConfigs": {
      "zh-Hans": {
        "label": "简体中文",
        "direction": "ltr"
      },
      "en": {
        "label": "English",
        "direction": "ltr"
      }
    },
    "path": "i18n"
  },
  "presets": [
    [
      "classic",
      {
        "docs": {
          "sidebarPath": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/sidebars.js",
          "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/"
        },
        "blog": {
          "showReadingTime": true,
          "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/"
        },
        "theme": {
          "customCss": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/src/css/custom.css"
        }
      }
    ]
  ],
  "themeConfig": {
    "image": "img/social-card.jpg",
    "navbar": {
      "title": "拾光忆栈",
      "logo": {
        "alt": "拾光忆栈 Logo",
        "src": "img/logo.svg"
      },
      "items": [
        {
          "type": "docSidebar",
          "sidebarId": "tutorialSidebar",
          "position": "left",
          "label": "文档"
        },
        {
          "to": "/examples",
          "label": "示例",
          "position": "left"
        },
        {
          "to": "/blog",
          "label": "博客",
          "position": "left"
        },
        {
          "to": "/docs/support",
          "label": "订阅",
          "position": "left"
        },
        {
          "type": "localeDropdown",
          "position": "right",
          "dropdownItemsBefore": [],
          "dropdownItemsAfter": []
        },
        {
          "href": "https://github.com/AIPlayZone/MemoryKeeper",
          "label": "GitHub",
          "position": "right"
        }
      ],
      "hideOnScroll": false
    },
    "footer": {
      "style": "dark",
      "links": [
        {
          "title": "文档",
          "items": [
            {
              "label": "教程",
              "to": "/docs/getting-started/installation"
            }
          ]
        },
        {
          "title": "社区",
          "items": [
            {
              "label": "GitHub Discussions",
              "href": "https://github.com/AIPlayZone/MemoryKeeper/discussions"
            }
          ]
        },
        {
          "title": "更多",
          "items": [
            {
              "label": "GitHub",
              "href": "https://github.com/AIPlayZone/MemoryKeeper"
            }
          ]
        }
      ],
      "copyright": "Copyright © 2025 拾光忆栈. Built with Docusaurus."
    },
    "prism": {
      "theme": {
        "plain": {
          "color": "#393A34",
          "backgroundColor": "#f6f8fa"
        },
        "styles": [
          {
            "types": [
              "comment",
              "prolog",
              "doctype",
              "cdata"
            ],
            "style": {
              "color": "#999988",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "namespace"
            ],
            "style": {
              "opacity": 0.7
            }
          },
          {
            "types": [
              "string",
              "attr-value"
            ],
            "style": {
              "color": "#e3116c"
            }
          },
          {
            "types": [
              "punctuation",
              "operator"
            ],
            "style": {
              "color": "#393A34"
            }
          },
          {
            "types": [
              "entity",
              "url",
              "symbol",
              "number",
              "boolean",
              "variable",
              "constant",
              "property",
              "regex",
              "inserted"
            ],
            "style": {
              "color": "#36acaa"
            }
          },
          {
            "types": [
              "atrule",
              "keyword",
              "attr-name",
              "selector"
            ],
            "style": {
              "color": "#00a4db"
            }
          },
          {
            "types": [
              "function",
              "deleted",
              "tag"
            ],
            "style": {
              "color": "#d73a49"
            }
          },
          {
            "types": [
              "function-variable"
            ],
            "style": {
              "color": "#6f42c1"
            }
          },
          {
            "types": [
              "tag",
              "selector",
              "keyword"
            ],
            "style": {
              "color": "#00009f"
            }
          }
        ]
      },
      "darkTheme": {
        "plain": {
          "color": "#F8F8F2",
          "backgroundColor": "#282A36"
        },
        "styles": [
          {
            "types": [
              "prolog",
              "constant",
              "builtin"
            ],
            "style": {
              "color": "rgb(189, 147, 249)"
            }
          },
          {
            "types": [
              "inserted",
              "function"
            ],
            "style": {
              "color": "rgb(80, 250, 123)"
            }
          },
          {
            "types": [
              "deleted"
            ],
            "style": {
              "color": "rgb(255, 85, 85)"
            }
          },
          {
            "types": [
              "changed"
            ],
            "style": {
              "color": "rgb(255, 184, 108)"
            }
          },
          {
            "types": [
              "punctuation",
              "symbol"
            ],
            "style": {
              "color": "rgb(248, 248, 242)"
            }
          },
          {
            "types": [
              "string",
              "char",
              "tag",
              "selector"
            ],
            "style": {
              "color": "rgb(255, 121, 198)"
            }
          },
          {
            "types": [
              "keyword",
              "variable"
            ],
            "style": {
              "color": "rgb(189, 147, 249)",
              "fontStyle": "italic"
            }
          },
          {
            "types": [
              "comment"
            ],
            "style": {
              "color": "rgb(98, 114, 164)"
            }
          },
          {
            "types": [
              "attr-name"
            ],
            "style": {
              "color": "rgb(241, 250, 140)"
            }
          }
        ]
      },
      "additionalLanguages": [],
      "magicComments": [
        {
          "className": "theme-code-block-highlighted-line",
          "line": "highlight-next-line",
          "block": {
            "start": "highlight-start",
            "end": "highlight-end"
          }
        }
      ]
    },
    "colorMode": {
      "defaultMode": "light",
      "disableSwitch": false,
      "respectPrefersColorScheme": false
    },
    "docs": {
      "versionPersistence": "localStorage",
      "sidebar": {
        "hideable": false,
        "autoCollapseCategories": false
      }
    },
    "metadata": [],
    "tableOfContents": {
      "minHeadingLevel": 2,
      "maxHeadingLevel": 3
    }
  },
  "baseUrlIssueBanner": true,
  "onDuplicateRoutes": "warn",
  "staticDirectories": [
    "static"
  ],
  "customFields": {},
  "plugins": [],
  "themes": [],
  "scripts": [],
  "headTags": [],
  "stylesheets": [],
  "clientModules": [],
  "titleDelimiter": "|",
  "noIndex": false,
  "markdown": {
    "mermaid": false
  }
};
