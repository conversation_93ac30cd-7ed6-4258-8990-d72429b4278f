export default {
  '__comp---site-src-pages-examples-js-485-ddb': [() => import(/* webpackChunkName: '__comp---site-src-pages-examples-js-485-ddb' */ '@site/src/pages/examples.js'), '@site/src/pages/examples.js', require.resolveWeak('@site/src/pages/examples.js')],
  '__comp---site-src-pages-index-jsc-4-f-f99': [() => import(/* webpackChunkName: '__comp---site-src-pages-index-jsc-4-f-f99' */ '@site/src/pages/index.js'), '@site/src/pages/index.js', require.resolveWeak('@site/src/pages/index.js')],
  '__comp---theme-debug-config-23-a-2ff': [() => import(/* webpackChunkName: '__comp---theme-debug-config-23-a-2ff' */ '@theme/DebugConfig'), '@theme/DebugConfig', require.resolveWeak('@theme/DebugConfig')],
  '__comp---theme-debug-contentba-8-ce7': [() => import(/* webpackChunkName: '__comp---theme-debug-contentba-8-ce7' */ '@theme/DebugContent'), '@theme/DebugContent', require.resolveWeak('@theme/DebugContent')],
  '__comp---theme-debug-global-dataede-0fa': [() => import(/* webpackChunkName: '__comp---theme-debug-global-dataede-0fa' */ '@theme/DebugGlobalData'), '@theme/DebugGlobalData', require.resolveWeak('@theme/DebugGlobalData')],
  '__comp---theme-debug-registry-679-501': [() => import(/* webpackChunkName: '__comp---theme-debug-registry-679-501' */ '@theme/DebugRegistry'), '@theme/DebugRegistry', require.resolveWeak('@theme/DebugRegistry')],
  '__comp---theme-debug-routes-946-699': [() => import(/* webpackChunkName: '__comp---theme-debug-routes-946-699' */ '@theme/DebugRoutes'), '@theme/DebugRoutes', require.resolveWeak('@theme/DebugRoutes')],
  '__comp---theme-debug-site-metadata-68-e-3d4': [() => import(/* webpackChunkName: '__comp---theme-debug-site-metadata-68-e-3d4' */ '@theme/DebugSiteMetadata'), '@theme/DebugSiteMetadata', require.resolveWeak('@theme/DebugSiteMetadata')],
  '__comp---theme-doc-item-178-a40': [() => import(/* webpackChunkName: '__comp---theme-doc-item-178-a40' */ '@theme/DocItem'), '@theme/DocItem', require.resolveWeak('@theme/DocItem')],
  '__comp---theme-doc-page-1-be-9be': [() => import(/* webpackChunkName: '__comp---theme-doc-page-1-be-9be' */ '@theme/DocPage'), '@theme/DocPage', require.resolveWeak('@theme/DocPage')],
  'allContent---memory-keeper-docusaurus-debug-content-246-2a4': [() => import(/* webpackChunkName: 'allContent---memory-keeper-docusaurus-debug-content-246-2a4' */ '~debug/default/docusaurus-debug-all-content-673.json'), '~debug/default/docusaurus-debug-all-content-673.json', require.resolveWeak('~debug/default/docusaurus-debug-all-content-673.json')],
  'config---memory-keeper-examples-5-e-9-05e': [() => import(/* webpackChunkName: 'config---memory-keeper-examples-5-e-9-05e' */ '@generated/docusaurus.config'), '@generated/docusaurus.config', require.resolveWeak('@generated/docusaurus.config')],
  'content---memory-keeper-docs-about-3-d-8-cbc': [() => import(/* webpackChunkName: 'content---memory-keeper-docs-about-3-d-8-cbc' */ '@site/docs/about.md'), '@site/docs/about.md', require.resolveWeak('@site/docs/about.md')],
  'content---memory-keeper-docs-changelog-9-be-8eb': [() => import(/* webpackChunkName: 'content---memory-keeper-docs-changelog-9-be-8eb' */ '@site/docs/changelog.md'), '@site/docs/changelog.md', require.resolveWeak('@site/docs/changelog.md')],
  'content---memory-keeper-docs-getting-started-installation-54-f-9f7': [() => import(/* webpackChunkName: 'content---memory-keeper-docs-getting-started-installation-54-f-9f7' */ '@site/docs/getting-started/installation.md'), '@site/docs/getting-started/installation.md', require.resolveWeak('@site/docs/getting-started/installation.md')],
  'content---memory-keeper-docs-supportd-9-e-47b': [() => import(/* webpackChunkName: 'content---memory-keeper-docs-supportd-9-e-47b' */ '@site/docs/support.md'), '@site/docs/support.md', require.resolveWeak('@site/docs/support.md')],
  'plugin---memory-keeper-docs-90-b-e74': [() => import(/* webpackChunkName: 'plugin---memory-keeper-docs-90-b-e74' */ '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-docs/default/plugin-route-context-module-100.json'), '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-docs/default/plugin-route-context-module-100.json', require.resolveWeak('/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-docs/default/plugin-route-context-module-100.json')],
  'plugin---memory-keeper-docusaurus-debugd-69-419': [() => import(/* webpackChunkName: 'plugin---memory-keeper-docusaurus-debugd-69-419' */ '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-debug/default/plugin-route-context-module-100.json'), '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-debug/default/plugin-route-context-module-100.json', require.resolveWeak('/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-debug/default/plugin-route-context-module-100.json')],
  'plugin---memory-keeper-examplesd-63-599': [() => import(/* webpackChunkName: 'plugin---memory-keeper-examplesd-63-599' */ '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-pages/default/plugin-route-context-module-100.json'), '/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-pages/default/plugin-route-context-module-100.json', require.resolveWeak('/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/.docusaurus/docusaurus-plugin-content-pages/default/plugin-route-context-module-100.json')],
  'versionMetadata---memory-keeper-docs-935-ca2': [() => import(/* webpackChunkName: 'versionMetadata---memory-keeper-docs-935-ca2' */ '~docs/default/version-current-metadata-prop-751.json'), '~docs/default/version-current-metadata-prop-751.json', require.resolveWeak('~docs/default/version-current-metadata-prop-751.json')],};
