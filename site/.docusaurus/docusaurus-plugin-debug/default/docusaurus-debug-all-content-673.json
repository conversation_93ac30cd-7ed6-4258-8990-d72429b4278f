{"docusaurus-plugin-content-docs": {"default": {"loadedVersions": [{"versionName": "current", "label": "Next", "banner": null, "badge": false, "noIndex": false, "className": "docs-version-current", "path": "/MemoryKeeper/docs", "tagsPath": "/MemoryKeeper/docs/tags", "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/docs", "editUrlLocalized": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/i18n/zh-Hans/docusaurus-plugin-content-docs/current", "isLast": true, "routePriority": -1, "sidebarFilePath": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/sidebars.js", "contentPath": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/docs", "contentPathLocalized": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/site/i18n/zh-Hans/docusaurus-plugin-content-docs/current", "docs": [{"unversionedId": "about", "id": "about", "title": "关于拾光忆栈", "description": "拾光忆栈（MemoryKeeper）是一个专注于个人记忆和知识管理的Chrome扩展，它允许您从网页中保存文本片段、图片和视频，并通过分类、标签和搜索功能进行组织和管理。", "source": "@site/docs/about.md", "sourceDirName": ".", "slug": "/about", "permalink": "/MemoryKeeper/docs/about", "draft": false, "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/docs/about.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "next": {"title": "安装指南", "permalink": "/MemoryKeeper/docs/getting-started/installation"}}, {"unversionedId": "changelog", "id": "changelog", "title": "更新日志", "description": "本页面记录了拾光忆栈的所有版本更新内容。", "source": "@site/docs/changelog.md", "sourceDirName": ".", "slug": "/changelog", "permalink": "/MemoryKeeper/docs/changelog", "draft": false, "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/docs/changelog.md", "tags": [], "version": "current", "sidebarPosition": 8, "frontMatter": {"sidebar_position": 8}, "sidebar": "tutorialSidebar", "previous": {"title": "订阅与支持", "permalink": "/MemoryKeeper/docs/support"}}, {"unversionedId": "getting-started/installation", "id": "getting-started/installation", "title": "安装指南", "description": "本指南将帮助您在Chrome浏览器中安装拾光忆栈扩展。", "source": "@site/docs/getting-started/installation.md", "sourceDirName": "getting-started", "slug": "/getting-started/installation", "permalink": "/MemoryKeeper/docs/getting-started/installation", "draft": false, "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/docs/getting-started/installation.md", "tags": [], "version": "current", "sidebarPosition": 1, "frontMatter": {"sidebar_position": 1}, "sidebar": "tutorialSidebar", "previous": {"title": "关于拾光忆栈", "permalink": "/MemoryKeeper/docs/about"}, "next": {"title": "订阅与支持", "permalink": "/MemoryKeeper/docs/support"}}, {"unversionedId": "support", "id": "support", "title": "订阅与支持", "description": "拾光忆栈提供多种支持选项，帮助您获得最佳使用体验。", "source": "@site/docs/support.md", "sourceDirName": ".", "slug": "/support", "permalink": "/MemoryKeeper/docs/support", "draft": false, "editUrl": "https://github.com/AIPlayZone/MemoryKeeper/tree/main/site/docs/support.md", "tags": [], "version": "current", "sidebarPosition": 7, "frontMatter": {"sidebar_position": 7}, "sidebar": "tutorialSidebar", "previous": {"title": "安装指南", "permalink": "/MemoryKeeper/docs/getting-started/installation"}, "next": {"title": "更新日志", "permalink": "/MemoryKeeper/docs/changelog"}}], "drafts": [], "sidebars": {"tutorialSidebar": [{"type": "doc", "id": "about"}, {"type": "category", "label": "快速开始", "items": [{"type": "doc", "id": "getting-started/installation"}], "collapsed": true, "collapsible": true}, {"type": "doc", "id": "support"}, {"type": "doc", "id": "changelog"}]}}]}}, "docusaurus-plugin-content-blog": {"default": {"blogSidebarTitle": "Recent posts", "blogPosts": [], "blogListPaginated": [], "blogTags": {}, "blogTagsListPath": "/MemoryKeeper/blog/tags", "blogTagsPaginated": []}}, "docusaurus-plugin-content-pages": {"default": [{"type": "jsx", "permalink": "/MemoryKeeper/examples", "source": "@site/src/pages/examples.js"}, {"type": "jsx", "permalink": "/MemoryKeeper/", "source": "@site/src/pages/index.js"}]}, "docusaurus-plugin-debug": {}, "docusaurus-theme-classic": {}, "docusaurus-bootstrap-plugin": {}, "docusaurus-mdx-fallback-plugin": {}}